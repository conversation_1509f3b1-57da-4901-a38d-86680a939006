import React, { useEffect, useRef, useState } from 'react';
import * as go from 'gojs';
import { GoJSNodeData, GoJSModelData, ColumnData } from '../types/DiagramTypes';
import { DocumentData, ConnectionData, loadData, createCompleteResultColumns, hasMissingRequiredFields, updateResultNodeData } from '../utils/dataModelUtils';

interface OrgChartEditorProps {
    width: string;
    height: string;
    context?: ComponentFramework.Context<any>;
    entityId?: string | null;
}



interface ColumnWithSource extends ColumnData {
    sourceDocumentId?: number;
    sourceDocumentName?: string;
    clickData?: any;
    clickTimestamp?: string;
    interactionType?: string;
}

export const OrgChartEditor: React.FC<OrgChartEditorProps> = ({ width, height, context, entityId }) => {
    const diagramRef = useRef<HTMLDivElement>(null);
    const [diagram, setDiagram] = useState<go.Diagram | null>(null);
    const [selectedColumnData, setSelectedColumnData] = useState<ColumnWithSource | null>(null);
    const [hoveredColumnId, setHoveredColumnId] = useState<number | null>(null);
    const [highlightedDocumentId, setHighlightedDocumentId] = useState<number | null>(null);

    const [showInteractionData, setShowInteractionData] = useState<boolean>(false);
    const [data, setData] = useState<DocumentData[]>([]);
    const [connections, setConnections] = useState<ConnectionData[]>([]);
    const [isLoading, setIsLoading] = useState(true);



    // Helper function to get column styling (without color coding)
    const getColumnBackgroundColor = (isUsed: boolean, isHovered: boolean): string => {
        if (isHovered) return '#F6F8FA';
        return isUsed ? '#F0F8E8' : '#F5F5F5';
    };

    // Helper function to get column border color
    const getColumnBorderColor = (isUsed: boolean): string => {
        return isUsed ? '#4CAF50' : '#DDD';
    };

    // Handle column hover events
    const handleColumnHover = (columnData: ColumnWithSource | null) => {
        if (columnData?.sourceDocumentId) {
            setHighlightedDocumentId(columnData.sourceDocumentId);
            setHoveredColumnId(columnData.id);
        } else {
            setHighlightedDocumentId(null);
            setHoveredColumnId(null);
        }
    };

    // Handle column click events
    const handleColumnClick = (columnData: ColumnWithSource, additionalData?: any) => {
        const enhancedColumnData = {
            ...columnData,
            clickData: additionalData || {},
            clickTimestamp: new Date().toISOString(),
            interactionType: 'column_click'
        };
        setSelectedColumnData(enhancedColumnData);
    };

    // Handle toggling the used field for a column
    const handleUsedFieldToggle = (columnData: ColumnWithSource, newUsedValue: boolean) => {
        if (!diagram) return;

        console.log(`Toggling used field for column ${columnData.id} (${columnData.field}) to ${newUsedValue}`);
        console.log('Column data:', columnData);

        diagram.startTransaction("toggle column used field");

        try {
            // Find the source document node
            let sourceDocumentId = columnData.sourceDocumentId;
            let sourceNode: go.Node | null = null;

            if (sourceDocumentId) {
                sourceNode = diagram.findNodeForKey(sourceDocumentId);
            }

            // If sourceDocumentId is missing or node not found, try to find it by searching all document nodes
            if (!sourceNode) {
                console.log('Source document ID missing or node not found, searching all document nodes...');

                const documentNodes = Array.from(diagram.nodes).filter(node => node.data.type === 'document');
                for (const docNode of documentNodes) {
                    if (docNode.data.columns) {
                        const matchingColumn = docNode.data.columns.find((col: ColumnData) =>
                            col.id === columnData.id ||
                            (col.field === columnData.field && col.valueDisplayLabel === columnData.valueDisplayLabel)
                        );
                        if (matchingColumn) {
                            sourceNode = docNode;
                            sourceDocumentId = docNode.data.key;
                            console.log(`Found source document: ${docNode.data.name} (ID: ${sourceDocumentId})`);
                            break;
                        }
                    }
                }
            }

            if (!sourceNode || !sourceDocumentId) {
                console.error('Could not find source document for column');
                diagram.commitTransaction("toggle column used field");
                return;
            }

            const sourceNodeData = sourceNode.data;
            if (!sourceNodeData.columns) {
                console.error('Source node has no columns');
                return;
            }

            // If setting to used=true, check for conflicts with other columns of the same field
            if (newUsedValue) {
                console.log(`Checking for conflicts with field: ${columnData.field}`);

                // Find all document nodes (not just connected to result nodes)
                const documentNodes = Array.from(diagram.nodes).filter(node => node.data.type === 'document');

                for (const docNode of documentNodes) {
                    if (docNode.data.columns && docNode.data.key !== sourceDocumentId) {
                        const conflictingColumns = docNode.data.columns.filter((col: ColumnData) =>
                            col.field === columnData.field && col.used && col.id !== columnData.id
                        );

                        if (conflictingColumns.length > 0) {
                            console.log(`Found ${conflictingColumns.length} conflicting columns in document ${docNode.data.name} for field ${columnData.field}, setting them to unused`);

                            // Set conflicting columns to used=false
                            const updatedColumns = docNode.data.columns.map((col: ColumnData) =>
                                conflictingColumns.some((conflict: ColumnData) => conflict.id === col.id)
                                    ? { ...col, used: false }
                                    : col
                            );

                            const model = diagram.model as go.TreeModel;
                            model.setDataProperty(docNode.data, "columns", updatedColumns);

                            // Update used columns count
                            const usedCount = updatedColumns.filter((col: ColumnData) => col.used).length;
                            model.setDataProperty(docNode.data, "usedColumnsCount", usedCount);

                            // Update CRM if in production mode
                            if (!isDevEnvironment(context) && context) {
                                updateCRMColumnUsage(docNode.data.key, updatedColumns, context);
                            }
                        }
                    }
                }
            }

            // Update the current column
            const updatedColumns = sourceNodeData.columns.map((col: ColumnData) =>
                col.id === columnData.id ? { ...col, used: newUsedValue } : col
            );

            const model = diagram.model as go.TreeModel;
            model.setDataProperty(sourceNodeData, "columns", updatedColumns);

            // Update used columns count
            const usedCount = updatedColumns.filter((col: ColumnData) => col.used).length;
            model.setDataProperty(sourceNodeData, "usedColumnsCount", usedCount);

            // Update the selected column data to reflect the change
            setSelectedColumnData(prev => prev ? { ...prev, used: newUsedValue } : null);

            // Update CRM if in production mode
            if (!isDevEnvironment(context) && context) {
                updateCRMColumnUsage(sourceDocumentId, updatedColumns, context);
            }

            // Update result nodes to reflect the changes
            const resultNodes = Array.from(diagram.nodes).filter(node => node.data.type === 'result');
            for (const resultNode of resultNodes) {
                const connectedDocIds = Array.from(diagram.links)
                    .filter(link => link.data.to === resultNode.data.key)
                    .map(link => link.data.from);

                // Get current document data from the diagram (not stale state)
                const currentDocuments: DocumentData[] = [];
                for (const docId of connectedDocIds) {
                    const docNode = diagram.findNodeForKey(docId);
                    if (docNode && docNode.data.type !== 'result') {
                        currentDocuments.push({
                            id: docNode.data.key,
                            name: docNode.data.name,
                            columns: docNode.data.columns || [],
                            type: docNode.data.type
                        });
                    }
                }

                // Get updated result data using current diagram data
                const resultData = updateResultNodeData(connectedDocIds, currentDocuments);

                model.setDataProperty(resultNode.data, "columns", resultData.columns);
                model.setDataProperty(resultNode.data, "usedColumnsCount", resultData.columns.length);
                model.setDataProperty(resultNode.data, "hasMissingRequiredFields", resultData.hasMissingFields);
            }

            // Update the main data state to keep it in sync
            const updatedData = data.map(doc => {
                const diagramNode = diagram.findNodeForKey(doc.id);
                if (diagramNode && diagramNode.data.columns) {
                    return {
                        ...doc,
                        columns: diagramNode.data.columns
                    };
                }
                return doc;
            });
            setData(updatedData);

            console.log(`Successfully toggled column ${columnData.id} used field to ${newUsedValue}`);

        } catch (error) {
            console.error('Error toggling used field:', error);
        } finally {
            diagram.commitTransaction("toggle column used field");
        }
    };

    // Load data on component mount
    useEffect(() => {
        const loadDataAsync = async () => {
            setIsLoading(true);
            try {
                const result = await loadData(context, entityId);
                setData(result.data);
                setConnections(result.connections);
                console.log('Data loaded:', result.data.length, 'documents,', result.connections.length, 'connections');
                if (entityId) {
                    console.log('Loaded data for OCR header:', entityId);
                }
            } catch (error) {
                console.error('Error loading data:', error);
                // Fallback to empty data
                setData([]);
                setConnections([]);
            } finally {
                setIsLoading(false);
            }
        };

        loadDataAsync();
    }, [context, entityId]);

    // Transform data to GoJS format
    const transformDataToGoJS = (): GoJSModelData => {
        const nodeDataArray: GoJSNodeData[] = data.map(item => {
            if (item.type === 'result') {
                // Find connected documents for this result node
                const connectedDocIds = connections
                    .filter(conn => conn.target === item.id)
                    .map(conn => conn.source);
                
                const connectedDocs = data.filter(doc =>
                    connectedDocIds.includes(doc.id) && doc.type !== 'result'
                );
                
                // Get all used columns from connected documents with source info
                const usedColumns = connectedDocs.flatMap(doc =>
                    (doc.columns?.filter(col => col.used) || []).map(col => ({
                        ...col,
                        sourceDocumentId: doc.id,
                        sourceDocumentName: doc.name
                    }))
                );

                // Create complete result columns including required fields
                const completeResultColumns = createCompleteResultColumns(usedColumns);

                // Check if any required fields are missing
                const hasMissingFields = hasMissingRequiredFields(completeResultColumns);

                // Set color based on missing fields - subtle reddish tint if missing
                const resultColor = hasMissingFields ? '#E8B4B4' : '#4CAF50'; // Light reddish vs green

                return {
                    key: item.id,
                    name: item.name,
                    type: 'result' as const,
                    category: 'result',
                    color: resultColor,
                    usedColumnsCount: usedColumns.length,
                    totalColumnsCount: connectedDocs.reduce((sum, doc) => sum + (doc.columns?.length || 0), 0),
                    columns: completeResultColumns,
                    hasMissingRequiredFields: hasMissingFields
                };
            } else {
                // Document node
                const connection = connections.find(conn => conn.source === item.id);
                const usedColumns = item.columns?.filter(col => col.used) || [];
                
                return {
                    key: item.id,
                    name: item.name,
                    type: 'document' as const,
                    category: 'document',
                    color: '#2196F3',
                    parent: connection?.target,
                    columns: item.columns,
                    usedColumnsCount: usedColumns.length,
                    totalColumnsCount: item.columns?.length || 0
                };
            }
        });

        return { nodeDataArray };
    };

    useEffect(() => {
        if (!diagramRef.current || isLoading || data.length === 0) return;

        console.log('Initializing GoJS diagram...');

        // Create the diagram
        const $ = go.GraphObject.make;
        const myDiagram = $(go.Diagram, diagramRef.current, {
            'undoManager.isEnabled': true,
            allowDelete: true, // Allow users to delete links and nodes
            layout: $(go.TreeLayout, {
                angle: 90,
                layerSpacing: 80,
                alternateAngle: 90,
                alternateLayerSpacing: 80,
                alternateAlignment: go.TreeAlignment.Bus,
                alternateNodeSpacing: 60
            }),
            
        });

        // Add a listener for model changes to detect link removal and creation
        myDiagram.addModelChangedListener((e) => {
            console.log('Model changed:', e.change, e.modelChange, e);

            // Check if this is a link removal event (for TreeModel, links are represented by parent relationships)
            if (e.change === go.ChangeType.Property && e.propertyName === "parent" && e.newValue === undefined && e.object) {
                console.log('Link deleted - node disconnected from parent:', e.object);
                const oldParentKey = e.oldValue;
                const disconnectedNodeKey = e.object.key;

                // First, update the disconnected document's columns to used: false
                if (e.object.type === 'document' && e.object.columns) {
                    console.log('Setting disconnected document columns to used: false:', disconnectedNodeKey);

                    myDiagram.startTransaction("update disconnected document columns");

                    const updatedColumns = e.object.columns.map((col: ColumnData) => ({
                        ...col,
                        used: false
                    }));

                    const model = myDiagram.model as go.TreeModel;
                    model.setDataProperty(e.object, "columns", updatedColumns);
                    model.setDataProperty(e.object, "usedColumnsCount", 0);

                    // Update CRM if in production mode
                    if (!isDevEnvironment(context) && context && typeof disconnectedNodeKey === 'number') {
                        updateCRMColumnUsage(disconnectedNodeKey, updatedColumns, context);
                    }

                    myDiagram.commitTransaction("update disconnected document columns");
                }

                // Then, check if the old parent was a result node and update it
                const oldParentNode = myDiagram.findNodeForKey(oldParentKey);
                if (oldParentNode && oldParentNode.data.type === 'result') {
                    console.log('Updating result node columns after link deletion:', oldParentKey);
                    // Update the result node's columns
                    updateResultNodeColumns(oldParentKey);
                }
            }

            // Check if this is a link creation event (for TreeModel, new parent relationship)
            if (e.change === go.ChangeType.Property && e.propertyName === "parent" && e.newValue !== undefined && e.object) {
                console.log('Link created - node connected to new parent:', e.object);
                const newParentKey = e.newValue;
                const nodeKey = e.object.key;

                // Check if the new parent is a result node and the connected node is a document
                const newParentNode = myDiagram.findNodeForKey(newParentKey);
                const connectedNode = myDiagram.findNodeForKey(nodeKey);

                if (newParentNode && newParentNode.data.type === 'result' &&
                    connectedNode && connectedNode.data.type === 'document' &&
                    typeof newParentKey === 'number' && typeof nodeKey === 'number') {
                    console.log('Document connected to result node - updating columns:', newParentKey);
                    // Update the result node's columns with auto-activation for newly connected document
                    updateResultNodeColumns(newParentKey, nodeKey);
                }
            }

            // Also check for direct link removal (in case we switch to GraphLinksModel later)
            if (e.change === go.ChangeType.Remove && e.modelChange === "linkDataArray") {
                console.log('Direct link removed:', e.oldValue);
                const linkData = e.oldValue;
                if (linkData && linkData.to && linkData.from) {
                    // First, update the disconnected document's columns
                    const sourceNode = myDiagram.findNodeForKey(linkData.from);
                    if (sourceNode && sourceNode.data.type === 'document' && sourceNode.data.columns) {
                        console.log('Setting disconnected document columns to used: false (direct link):', linkData.from);

                        myDiagram.startTransaction("update disconnected document columns (direct)");

                        const updatedColumns = sourceNode.data.columns.map((col: ColumnData) => ({
                            ...col,
                            used: false
                        }));

                        const model = myDiagram.model as go.TreeModel;
                        model.setDataProperty(sourceNode.data, "columns", updatedColumns);
                        model.setDataProperty(sourceNode.data, "usedColumnsCount", 0);

                        // Update CRM if in production mode
                        if (!isDevEnvironment(context) && context && typeof linkData.from === 'number') {
                            updateCRMColumnUsage(linkData.from, updatedColumns, context);
                        }

                        myDiagram.commitTransaction("update disconnected document columns (direct)");
                    }

                    // Then, check if target is a result node and update it
                    const targetNode = myDiagram.findNodeForKey(linkData.to);
                    if (targetNode && targetNode.data.type === 'result') {
                        console.log('Updating result node columns after direct link deletion:', linkData.to);
                        // Update the result node's columns
                        updateResultNodeColumns(linkData.to);
                    }
                }
            }

            // Also check for direct link addition (in case we switch to GraphLinksModel later)
            if (e.change === go.ChangeType.Insert && e.modelChange === "linkDataArray") {
                console.log('Direct link added:', e.newValue);
                const linkData = e.newValue;
                if (linkData && linkData.to) {
                    // Check if target is a result node
                    const targetNode = myDiagram.findNodeForKey(linkData.to);
                    if (targetNode && targetNode.data.type === 'result') {
                        console.log('Updating result node columns after direct link addition:', linkData.to);
                        // Update the result node's columns
                        updateResultNodeColumns(linkData.to);
                    }
                }
            }
        });

        // Add a DiagramEvent listener for when parts are deleted
        myDiagram.addDiagramListener("SelectionDeleted", (e) => {
            console.log('Selection deleted:', e.subject);
            const deletedParts = e.subject;

            // Check if any deleted parts were links
            deletedParts.each((part: go.Part) => {
                if (part instanceof go.Link) {
                    console.log('Link deleted via selection:', part);
                    const linkData = part.data;
                    if (linkData && linkData.to && linkData.from) {
                        // First, update the disconnected document's columns
                        const sourceNode = myDiagram.findNodeForKey(linkData.from);
                        if (sourceNode && sourceNode.data.type === 'document' && sourceNode.data.columns) {
                            console.log('Setting disconnected document columns to used: false (selection):', linkData.from);

                            myDiagram.startTransaction("update disconnected document columns (selection)");

                            const updatedColumns = sourceNode.data.columns.map((col: ColumnData) => ({
                                ...col,
                                used: false
                            }));

                            const model = myDiagram.model as go.TreeModel;
                            model.setDataProperty(sourceNode.data, "columns", updatedColumns);
                            model.setDataProperty(sourceNode.data, "usedColumnsCount", 0);

                            // Update CRM if in production mode
                            if (!isDevEnvironment(context) && context && typeof linkData.from === 'number') {
                                updateCRMColumnUsage(linkData.from, updatedColumns, context);
                            }

                            myDiagram.commitTransaction("update disconnected document columns (selection)");
                        }

                        // Then, check if target is a result node and update it
                        const targetNode = myDiagram.findNodeForKey(linkData.to);
                        if (targetNode && targetNode.data.type === 'result') {
                            console.log('Updating result node columns after link selection deletion:', linkData.to);
                            // Update the result node's columns
                            updateResultNodeColumns(linkData.to);
                        }
                    }
                }
            });
        });

        // Add a DiagramEvent listener for when new links are drawn
        myDiagram.addDiagramListener("LinkDrawn", (e) => {
            console.log('New link drawn:', e.subject);
            const newLink = e.subject as go.Link;

            if (newLink && newLink.toNode && newLink.fromNode) {
                const targetKey = newLink.toNode.key;
                const sourceKey = newLink.fromNode.key;

                console.log('Link drawn from', sourceKey, 'to', targetKey);

                // Check if target is a result node and source is a document
                const targetNode = myDiagram.findNodeForKey(targetKey);
                const sourceNode = myDiagram.findNodeForKey(sourceKey);

                if (targetNode && targetNode.data.type === 'result' &&
                    sourceNode && sourceNode.data.type === 'document' &&
                    typeof targetKey === 'number' && typeof sourceKey === 'number') {
                    console.log('Document linked to result node - updating columns:', targetKey);
                    // Update the result node's columns with auto-activation for newly connected document
                    updateResultNodeColumns(targetKey, sourceKey);
                }
            }
        });

        // Add a DiagramEvent listener for when links are reconnected
        myDiagram.addDiagramListener("LinkRelinked", (e) => {
            console.log('Link relinked:', e.subject);
            const relinkedLink = e.subject as go.Link;
            const oldPort = e.parameter; // The port that the link was disconnected from

            if (relinkedLink && relinkedLink.toNode && relinkedLink.fromNode) {
                const newTargetKey = relinkedLink.toNode.key;
                const newSourceKey = relinkedLink.fromNode.key;

                console.log('Link relinked from', newSourceKey, 'to', newTargetKey);

                // Update the new target if it's a result node
                const newTargetNode = myDiagram.findNodeForKey(newTargetKey);
                const newSourceNode = myDiagram.findNodeForKey(newSourceKey);

                if (newTargetNode && newTargetNode.data.type === 'result' &&
                    newSourceNode && newSourceNode.data.type === 'document' &&
                    typeof newTargetKey === 'number' && typeof newSourceKey === 'number') {
                    console.log('Document relinked to result node - updating columns:', newTargetKey);
                    // Update with auto-activation for newly connected document
                    updateResultNodeColumns(newTargetKey, newSourceKey);
                }

                // Also update the old target if we can determine it from the old port
                if (oldPort && oldPort.part && oldPort.part.data.type === 'result') {
                    const oldTargetKey = oldPort.part.key;
                    if (typeof oldTargetKey === 'number') {
                        console.log('Updating old result node after relink:', oldTargetKey);
                        updateResultNodeColumns(oldTargetKey);
                    }
                }
            }
        });

        // Function to update result node columns based on connected documents
        const updateResultNodeColumns = (resultNodeKey: number, newlyConnectedDocumentKey?: number) => {
            console.log('=== updateResultNodeColumns called for result node:', resultNodeKey, 'newly connected doc:', newlyConnectedDocumentKey);
            const model = myDiagram.model as go.TreeModel;

            // For TreeModel, we need to find nodes that have this result node as their parent
            const connectedDocIds: number[] = [];
            const disconnectedDocIds: number[] = [];

            // Track which documents were previously connected by checking the current result node's columns
            const previouslyConnectedDocs = new Set<number>();
            const currentResultNode = model.findNodeDataForKey(resultNodeKey);
            if (currentResultNode && currentResultNode.columns) {
                // Extract source document IDs from existing columns
                currentResultNode.columns.forEach((col: any) => {
                    if (col.sourceDocumentId && typeof col.sourceDocumentId === 'number') {
                        previouslyConnectedDocs.add(col.sourceDocumentId);
                    }
                });
            }

            // Method 1: Check all nodes in the model for parent relationships
            model.nodeDataArray.forEach((node: any) => {
                if (node.parent === resultNodeKey && node.type === 'document') {
                    connectedDocIds.push(node.key);
                    console.log('Found connected document via parent relationship:', node.key, node.name);
                }
            });

            // Method 2: Check for visual links (in case we're using GraphLinksModel)
            const linkConnectedIds: number[] = [];
            myDiagram.links.each(link => {
                if (link.toNode && link.fromNode &&
                    link.toNode.key === resultNodeKey &&
                    link.fromNode.data.type === 'document' &&
                    typeof link.fromNode.key === 'number') {
                    linkConnectedIds.push(link.fromNode.key);
                    console.log('Found connected document via visual link:', link.fromNode.key, link.fromNode.data.name);
                }
            });

            // Find disconnected documents (previously connected but not anymore)
            console.log('Checking for disconnected documents. Previously connected:', Array.from(previouslyConnectedDocs), 'Currently connected:', connectedDocIds, 'Link connected:', linkConnectedIds);

            previouslyConnectedDocs.forEach(docId => {
                if (!connectedDocIds.includes(docId) && !linkConnectedIds.includes(docId)) {
                    disconnectedDocIds.push(docId);
                    console.log('Found disconnected document:', docId);
                }
            });

            // Note: Disconnected documents are now handled immediately in the event handler
            // This section is kept for any edge cases but should rarely be needed
            if (disconnectedDocIds.length > 0) {
                console.log('Additional disconnected documents found (edge case):', disconnectedDocIds);

                myDiagram.startTransaction("update additional disconnected document columns");

                disconnectedDocIds.forEach(docId => {
                    const docNodeData = model.findNodeDataForKey(docId);

                    if (docNodeData && docNodeData.columns) {
                        // Update all columns to used: false in the diagram model
                        const updatedColumns = docNodeData.columns.map((col: ColumnData) => ({
                            ...col,
                            used: false
                        }));

                        model.setDataProperty(docNodeData, "columns", updatedColumns);
                        model.setDataProperty(docNodeData, "usedColumnsCount", 0);

                        // If in production mode, update CRM records
                        if (!isDevEnvironment(context) && context) {
                            updateCRMColumnUsage(docId, updatedColumns, context);
                        }
                    }
                });

                myDiagram.commitTransaction("update additional disconnected document columns");
            }

            // Combine both methods - use both parent relationships AND visual links
            const allConnectedIds = [...new Set([...connectedDocIds, ...linkConnectedIds])];
            console.log('Combined connected doc IDs:', allConnectedIds);

            // Convert model data to DocumentData format for the helper function
            const allDocuments: DocumentData[] = model.nodeDataArray.map((node: any) => ({
                id: node.key,
                name: node.name,
                columns: node.columns || [],
                type: node.type
            }));

            // Get the result node data first
            const resultNodeData = model.findNodeDataForKey(resultNodeKey);

            // Get existing result columns for auto-activation logic
            const existingResultColumns = resultNodeData?.columns || [];

            // Use the helper function to calculate new result data
            const resultData = updateResultNodeData(
                allConnectedIds,
                allDocuments,
                newlyConnectedDocumentKey,
                existingResultColumns
            );

            console.log('Updated result data:', {
                columnsCount: resultData.columns.length,
                hasMissingFields: resultData.hasMissingFields
            });
            if (resultNodeData) {
                console.log('Updating result node data:', resultNodeData.name);

                // Store current positions and sizes of all nodes before updating
                const nodeInfo = new Map<any, {location: go.Point, size: go.Size}>();
                myDiagram.nodes.each(node => {
                    nodeInfo.set(node.key, {
                        location: node.location.copy(),
                        size: node.actualBounds.size.copy()
                    });
                });

                // Completely disable automatic layout during update
                const originalLayout = myDiagram.layout;
                const noLayout = $(go.Layout);
                myDiagram.layout = noLayout;

                // Use a single transaction for the entire operation
                myDiagram.startTransaction("update result columns with required fields");

                try {
                    // Update the data with complete required fields
                    model.setDataProperty(resultNodeData, "columns", resultData.columns);
                    model.setDataProperty(resultNodeData, "usedColumnsCount", resultData.columns.filter(col => col.used).length);
                    model.setDataProperty(resultNodeData, "hasMissingRequiredFields", resultData.hasMissingFields);

                    // Update visual properties based on missing fields
                    const resultColor = resultData.hasMissingFields ? '#E8B4B4' : '#4CAF50';
                    model.setDataProperty(resultNodeData, "color", resultColor);

                    // Update document nodes if fields were auto-activated
                    if (resultData.updatedDocuments && resultData.updatedDocuments.length > 0) {
                        console.log('Updating document nodes with auto-activated fields:', resultData.updatedDocuments.length);

                        resultData.updatedDocuments.forEach(updatedDoc => {
                            const docNodeData = model.findNodeDataForKey(updatedDoc.id);
                            if (docNodeData && updatedDoc.columns) {
                                console.log(`Updating document ${updatedDoc.name} with auto-activated fields`);
                                model.setDataProperty(docNodeData, "columns", updatedDoc.columns);

                                // Update used columns count
                                const usedCount = updatedDoc.columns.filter(col => col.used).length;
                                model.setDataProperty(docNodeData, "usedColumnsCount", usedCount);

                                // Update CRM if in production mode
                                if (!isDevEnvironment(context) && context) {
                                    updateCRMColumnUsage(updatedDoc.id, updatedDoc.columns, context);
                                }
                            }
                        });
                    }

                    // Force immediate visual update without layout
                    myDiagram.updateAllTargetBindings();

                    // Restore all node positions immediately
                    myDiagram.nodes.each(node => {
                        const savedInfo = nodeInfo.get(node.key);
                        if (savedInfo) {
                            node.location = savedInfo.location;
                        }
                    });

                    myDiagram.commitTransaction("update result columns with required fields");

                    // Restore the original layout after everything is done
                    myDiagram.layout = originalLayout;

                    console.log('Result node updated successfully with required fields. Missing fields:', resultData.hasMissingFields);

                } catch (error) {
                    console.error('Error updating result node:', error);
                    myDiagram.rollbackTransaction();
                    // Restore layout even on error
                    myDiagram.layout = originalLayout;
                }
            } else {
                console.log('ERROR: Could not find result node data for key:', resultNodeKey);
            }
        };

        // Define document node template
        myDiagram.nodeTemplateMap.add('document',
            $(go.Node, 'Auto',
                $(go.Shape, 'RoundedRectangle',
                    {
                        portId: '',
                        cursor: 'pointer',
                        fromLinkable: true,
                        toLinkable: true,
                        minSize: new go.Size(400, 120)
                    },
                    new go.Binding('fill', '', (data: GoJSNodeData) =>
                        highlightedDocumentId === data.key ? '#FFE082' : '#E3F2FD'
                    ),
                    new go.Binding('stroke', '', (data: GoJSNodeData) =>
                        highlightedDocumentId === data.key ? '#FF9800' : '#2196F3'
                    ),
                    new go.Binding('strokeWidth', '', (data: GoJSNodeData) =>
                        highlightedDocumentId === data.key ? 3 : 2
                    )
                ),
                $(go.Panel, 'Vertical',
                    { margin: 10, maxSize: new go.Size(380, NaN) },
                    // Document name
                    $(go.TextBlock,
                        {
                            font: 'bold 14px sans-serif',
                            margin: new go.Margin(0, 0, 10, 0),
                            maxSize: new go.Size(360, NaN),
                            wrap: go.Wrap.Fit,
                            textAlign: 'center'
                        },
                        new go.Binding('text', 'name')
                    ),
                    // Column count summary
                    $(go.TextBlock,
                        {
                            font: '11px sans-serif',
                            stroke: '#666',
                            margin: new go.Margin(0, 0, 8, 0)
                        },
                        new go.Binding('text', '', (data: GoJSNodeData) =>
                            `📄 ${data.totalColumnsCount || 0} columns (${data.usedColumnsCount || 0} used)`
                        )
                    ),
                    // Columns list
                    $(go.Panel, 'Vertical',
                        {
                            margin: new go.Margin(6, 0, 0, 0),
                            maxSize: new go.Size(360, 250),
                            background: '#F8F9FA',
                            stretch: go.Stretch.Horizontal,
                            padding: new go.Margin(4, 0, 4, 0)
                        },
                        new go.Binding('itemArray', 'columns'),
                        {
                            itemTemplate:
                                $(go.Panel, 'Horizontal',
                                    {
                                        margin: new go.Margin(3, 6, 3, 6),
                                        cursor: 'pointer',
                                        minSize: new go.Size(340, 28),
                                        stretch: go.Stretch.Horizontal,
                                        click: (_: go.InputEvent, obj: go.GraphObject) => {
                                            // In GoJS itemTemplate, the data is bound to the individual item
                                            const columnData = (obj as any).data as ColumnData;
                                            // Get the parent node data by traversing up the visual tree
                                            let nodeData: any = null;
                                            let currentObj: go.GraphObject | null = obj;
                                            while (currentObj && !nodeData) {
                                                if (currentObj.part && currentObj.part instanceof go.Node) {
                                                    nodeData = currentObj.part.data;
                                                    break;
                                                }
                                                currentObj = currentObj.panel;
                                            }

                                            if (columnData) {
                                                const additionalData = {
                                                    'data-column-id': columnData.id,
                                                    'data-column-field-label': columnData.fieldDisplayLabel,
                                                    'data-column-value-label': columnData.valueDisplayLabel,
                                                    'data-column-field': columnData.field,
                                                    'data-column-type': columnData.fieldType,
                                                    'data-column-used': columnData.used,
                                                    'data-column-confidence': columnData.confidence,
                                                    'data-node-type': 'document',
                                                    'data-node-id': nodeData?.key || 'unknown',
                                                    'data-node-name': nodeData?.name || 'unknown',
                                                    'data-interaction-source': 'document-node'
                                                };
                                                handleColumnClick(columnData as ColumnWithSource, additionalData);
                                            }
                                        },
                                        mouseEnter: (_: go.InputEvent, obj: go.GraphObject) => {
                                            const columnData = (obj as any).data as ColumnData;
                                            setHoveredColumnId(columnData?.id || null);
                                        },
                                        mouseLeave: () => {
                                            setHoveredColumnId(null);
                                        }
                                    },
                                    new go.Binding('background', '', (column: ColumnData) =>
                                        getColumnBackgroundColor(column.used, hoveredColumnId === column.id)
                                    ),
                                    // Status indicator
                                    $(go.TextBlock,
                                        {
                                            font: 'bold 16px sans-serif',
                                            width: 24,
                                            height: 24,
                                            textAlign: 'center',
                                            margin: new go.Margin(2, 10, 2, 6),
                                            background: 'white'
                                        },
                                        new go.Binding('text', '', (column: ColumnData) => column.used ? '✓' : '○'),
                                        new go.Binding('stroke', '', (column: ColumnData) => column.used ? '#4CAF50' : '#999')
                                    ),
                                    // Column name (field + value)
                                    $(go.TextBlock,
                                        {
                                            font: 'bold 12px sans-serif',
                                            margin: new go.Margin(6, 8, 6, 0),
                                            maxSize: new go.Size(220, NaN),
                                            wrap: go.Wrap.Fit,
                                            textAlign: 'left'
                                        },
                                        new go.Binding('text', '', (column: ColumnData) =>
                                            `${column.fieldDisplayLabel}: ${column.valueDisplayLabel}`
                                        ),
                                        new go.Binding('stroke', '', (column: ColumnData) => column.used ? '#333' : '#666')
                                    ),
                                    // Confidence indicator
                                    $(go.TextBlock,
                                        {
                                            font: 'bold 11px sans-serif',
                                            margin: new go.Margin(6, 6, 6, 8),
                                            width: 50,
                                            textAlign: 'center',
                                            stroke: '#666',
                                            background: 'rgba(255,255,255,0.8)',
                                            alignment: go.Spot.Right
                                        },
                                        new go.Binding('text', '', (column: ColumnData) =>
                                            `${Math.round(column.confidence * 100)}%`
                                        )
                                    )
                                )
                        }
                    )
                )
            )
        );

        // Define result node template
        myDiagram.nodeTemplateMap.add('result',
            $(go.Node, 'Auto',
                $(go.Shape, 'RoundedRectangle',
                    {
                        portId: '',
                        cursor: 'pointer',
                        fromLinkable: true,
                        toLinkable: true,
                        minSize: new go.Size(440, 140),
                        strokeWidth: 2
                    },
                    // Dynamic fill based on missing required fields
                    new go.Binding('fill', '', (data: GoJSNodeData) =>
                        data.hasMissingRequiredFields ? '#F5E6E6' : '#E8F5E8' // Light reddish vs light green
                    ),
                    // Dynamic stroke based on missing required fields
                    new go.Binding('stroke', '', (data: GoJSNodeData) =>
                        data.hasMissingRequiredFields ? '#D32F2F' : '#4CAF50' // Red vs green border
                    )
                ),
                $(go.Panel, 'Vertical',
                    { margin: 10, maxSize: new go.Size(420, NaN) },
                    // Result name
                    $(go.TextBlock,
                        {
                            font: 'bold 16px sans-serif',
                            margin: new go.Margin(0, 0, 10, 0),
                            maxSize: new go.Size(400, NaN),
                            wrap: go.Wrap.Fit,
                            textAlign: 'center'
                        },
                        new go.Binding('text', 'name')
                    ),
                    // Used columns count
                    $(go.TextBlock,
                        {
                            font: '11px sans-serif',
                            stroke: '#666',
                            margin: new go.Margin(0, 0, 8, 0)
                        },
                        new go.Binding('text', '', (data: GoJSNodeData) =>
                            `🎯 ${data.usedColumnsCount || 0} used columns`
                        )
                    ),
                    // Used columns list
                    $(go.Panel, 'Vertical',
                        {
                            margin: new go.Margin(6, 0, 0, 0),
                         
                            background: '#F0F8F0',
                            stretch: go.Stretch.Horizontal,
                            padding: new go.Margin(4, 0, 4, 0)
                        },
                        new go.Binding('itemArray', 'columns'),
                        {
                            itemTemplate:
                                $(go.Panel, 'Horizontal',
                                    {
                                        margin: new go.Margin(3, 6, 3, 6),
                                        cursor: 'pointer',
                                        minSize: new go.Size(380, 30),
                                        stretch: go.Stretch.Horizontal,
                                        click: (_: go.InputEvent, obj: go.GraphObject) => {
                                            // In GoJS itemTemplate, the data is bound to the individual item
                                            const columnData = (obj as any).data as ColumnWithSource;
                                            // Get the parent node data by traversing up the visual tree
                                            let nodeData: any = null;
                                            let currentObj: go.GraphObject | null = obj;
                                            while (currentObj && !nodeData) {
                                                if (currentObj.part && currentObj.part instanceof go.Node) {
                                                    nodeData = currentObj.part.data;
                                                    break;
                                                }
                                                currentObj = currentObj.panel;
                                            }

                                            if (columnData) {
                                                const additionalData = {
                                                    'data-column-id': columnData.id,
                                                    'data-column-field-label': columnData.fieldDisplayLabel,
                                                    'data-column-value-label': columnData.valueDisplayLabel,
                                                    'data-column-field': columnData.field,
                                                    'data-column-type': columnData.fieldType,
                                                    'data-column-used': columnData.used,
                                                    'data-column-confidence': columnData.confidence,
                                                    'data-source-document-id': columnData.sourceDocumentId,
                                                    'data-source-document-name': columnData.sourceDocumentName,
                                                    'data-node-type': 'result',
                                                    'data-node-id': nodeData?.key || 'unknown',
                                                    'data-node-name': nodeData?.name || 'unknown',
                                                    'data-interaction-source': 'result-node'
                                                };
                                                handleColumnClick(columnData, additionalData);
                                            }
                                        },
                                        mouseEnter: (_: go.InputEvent, obj: go.GraphObject) => {
                                            const columnData = (obj as any).data as ColumnWithSource;
                                            handleColumnHover(columnData);
                                        },
                                        mouseLeave: () => {
                                            handleColumnHover(null);
                                        }
                                    },
                                    new go.Binding('background', '', (column: ColumnWithSource) =>
                                        getColumnBackgroundColor(column.used, hoveredColumnId === column.id)
                                    ),
                                    // Used indicator - different for missing required fields
                                    $(go.TextBlock,
                                        {
                                            font: 'bold 16px sans-serif',
                                            width: 24,
                                            height: 24,
                                            textAlign: 'center',
                                            margin: new go.Margin(2, 10, 2, 6),
                                            background: 'white'
                                        },
                                        new go.Binding('text', '', (column: ColumnWithSource) =>
                                            column.valueDisplayLabel && column.valueDisplayLabel.trim() !== '' ? '✓' : '⚠'
                                        ),
                                        new go.Binding('stroke', '', (column: ColumnWithSource) =>
                                            column.valueDisplayLabel && column.valueDisplayLabel.trim() !== '' ? '#4CAF50' : '#FF9800'
                                        )
                                    ),
                                    // Column name (field + value) - show "Missing" for empty required fields
                                    $(go.TextBlock,
                                        {
                                            font: 'bold 12px sans-serif',
                                            margin: new go.Margin(6, 8, 6, 0),
                                            maxSize: new go.Size(200, NaN),
                                            wrap: go.Wrap.Fit,
                                            textAlign: 'left'
                                        },
                                        new go.Binding('text', '', (column: ColumnWithSource) => {
                                            const hasValue = column.valueDisplayLabel && column.valueDisplayLabel.trim() !== '';
                                            const displayValue = hasValue ? column.valueDisplayLabel : 'Missing';
                                            return `${column.fieldDisplayLabel}: ${displayValue}`;
                                        }),
                                        new go.Binding('stroke', '', (column: ColumnWithSource) => {
                                            const hasValue = column.valueDisplayLabel && column.valueDisplayLabel.trim() !== '';
                                            return hasValue ? '#333' : '#D32F2F'; // Red text for missing values
                                        })
                                    ),
                                    // Confidence indicator
                                    $(go.TextBlock,
                                        {
                                            font: 'bold 11px sans-serif',
                                            margin: new go.Margin(6, 6, 6, 8),
                                            width: 50,
                                            textAlign: 'center',
                                            stroke: '#666',
                                            background: 'rgba(255,255,255,0.8)',
                                            alignment: go.Spot.Right
                                        },
                                        new go.Binding('text', '', (column: ColumnWithSource) =>
                                            `${Math.round(column.confidence * 100)}%`
                                        )
                                    ),
                                    // Source document indicator
                                    $(go.TextBlock,
                                        {
                                            font: 'bold 10px sans-serif',
                                            margin: new go.Margin(6, 6, 6, 8),
                                            stroke: '#666',
                                            maxSize: new go.Size(90, NaN),
                                            wrap: go.Wrap.Fit,
                                            textAlign: 'center',
                                            background: 'rgba(255,255,255,0.8)',
                                            alignment: go.Spot.Right
                                        },
                                        new go.Binding('text', '', (column: ColumnWithSource) =>
                                            column.sourceDocumentName ? `[${column.sourceDocumentName.split('.')[0]}]` : ''
                                        )
                                    )
                                )
                        }
                    )
                )
            )
        );

        // Define link template
        myDiagram.linkTemplate =
            $(go.Link,
                { routing: go.Routing.Orthogonal, corner: 5 },
                $(go.Shape, { strokeWidth: 2, stroke: '#555' }),
                $(go.Shape, { toArrow: 'Standard', stroke: '#555', fill: '#555' })
            );

        // Set the model
        const modelData = transformDataToGoJS();
        console.log('Model data:', modelData);
        myDiagram.model = new go.TreeModel(modelData.nodeDataArray);

        console.log('Diagram initialized successfully');
        setDiagram(myDiagram);

        return () => {
            myDiagram.div = null;
        };
    }, [data, connections, isLoading]);

    // Update diagram when highlight state changes
    useEffect(() => {
        if (diagram) {
            diagram.requestUpdate();
        }
    }, [diagram, highlightedDocumentId, hoveredColumnId]);



    // Show loading indicator while data is being fetched
    if (isLoading) {
        return (
            <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width,
                height,
                minHeight: '500px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                backgroundColor: '#F6F8FA'
            }}>
                <div style={{
                    textAlign: 'center',
                    color: '#586069'
                }}>
                    <div style={{ fontSize: '24px', marginBottom: '12px' }}>⏳</div>
                    <div style={{ fontSize: '14px' }}>
                        {entityId
                            ? `Loading OCR documents for current header...`
                            : 'Loading OCR document data...'
                        }
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div style={{
            display: 'flex',
            width,
            height,
            minHeight: '500px',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
        }}>
            {/* Main diagram area with legend overlay */}
            <div style={{
                position: 'relative',
                flexGrow: 1,
                backgroundColor: '#FAFBFC',
                border: '1px solid #E1E4E8',
                borderRadius: '6px'
            }}>
                <div
                    ref={diagramRef}
                    style={{
                        width: '100%',
                        height: '100%',
                        backgroundColor: 'transparent'
                    }}
                />
            </div>

            {/* Enhanced right panel for details */}
            <div style={{
                width: '350px',
                backgroundColor: 'white',
                border: '1px solid #E1E4E8',
                borderLeft: '1px solid #E1E4E8',
                borderRadius: '0 6px 6px 0',
                display: 'flex',
                flexDirection: 'column',
                boxShadow: '-2px 0 8px rgba(0,0,0,0.05)'
            }}>
                {/* Header */}
                <div style={{
                    padding: '16px',
                    borderBottom: '1px solid #E1E4E8',
                    backgroundColor: '#F6F8FA'
                }}>
                    <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    }}>
                        <h3 style={{
                            margin: 0,
                            fontSize: '16px',
                            fontWeight: '600',
                            color: '#24292E'
                        }}>
                            {selectedColumnData ? '🔍 Column Details' : '� Diagram Guide'}
                        </h3>
                        {selectedColumnData && (
                            <button
                                onClick={() => setSelectedColumnData(null)}
                                style={{
                                    background: 'none',
                                    border: '1px solid #D1D5DA',
                                    borderRadius: '4px',
                                    padding: '4px 8px',
                                    cursor: 'pointer',
                                    fontSize: '12px',
                                    color: '#586069'
                                }}
                                title="Clear selection"
                            >
                                ✕
                            </button>
                        )}
                    </div>
                </div>

                {/* Content area */}
                <div style={{
                    flex: 1,
                    padding: '16px',
                    overflowY: 'auto'
                }}>
                {selectedColumnData ? (
                    // Enhanced Column Details View
                    <>
                        {/* Source Document (moved to top) */}
                        {selectedColumnData.sourceDocumentName && (
                            <div style={{
                                padding: '12px',
                                backgroundColor: '#EFF6FF',
                                border: '1px solid #BFDBFE',
                                borderRadius: '6px',
                                marginBottom: '16px'
                            }}>
                                <div style={{
                                    fontSize: '12px',
                                    fontWeight: '600',
                                    color: '#1E40AF',
                                    marginBottom: '6px',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px'
                                }}>
                                    Source Document
                                </div>
                                <div style={{
                                    fontSize: '13px',
                                    color: '#1E3A8A',
                                    fontWeight: '500'
                                }}>
                                    📄 {selectedColumnData.sourceDocumentName}
                                </div>
                            </div>
                        )}

                        {/* Column header */}
                        <div style={{
                            padding: '16px',
                            backgroundColor: getColumnBackgroundColor(selectedColumnData.used, true),
                            border: `2px solid ${getColumnBorderColor(selectedColumnData.used)}`,
                            borderRadius: '8px',
                            marginBottom: '20px'
                        }}>
                            <div style={{
                                display: 'flex',
                                alignItems: 'flex-start',
                                gap: '12px'
                            }}>
                                <div style={{
                                    width: '6px',
                                    height: '40px',
                                    backgroundColor: getColumnBorderColor(selectedColumnData.used),
                                    borderRadius: '3px',
                                    flexShrink: 0
                                }}></div>
                                <div style={{ flex: 1 }}>
                                    <div style={{
                                        fontSize: '16px',
                                        fontWeight: '600',
                                        color: '#24292E',
                                        marginBottom: '4px',
                                        lineHeight: '1.3'
                                    }}>
                                        {selectedColumnData.fieldDisplayLabel}
                                    </div>
                                    <div style={{
                                        fontSize: '14px',
                                        color: '#586069',
                                        fontWeight: '500',
                                        padding: '4px 8px',
                                        borderRadius: '4px',
                                        display: 'inline-block'
                                    }}>
                                        {selectedColumnData.valueDisplayLabel}
                                    </div>
                                </div>
                                <div style={{
                                    fontSize: '20px',
                                    color: selectedColumnData.used ? '#28A745' : '#6A737D'
                                }}>
                                    {selectedColumnData.used ? '✓' : '○'}
                                </div>
                            </div>
                        </div>

                        {/* Confidence Score - Keep this visible */}
                        <div style={{
                            padding: '12px',
                            backgroundColor: '#F0F9FF',
                            border: '1px solid #BAE6FD',
                            borderRadius: '6px',
                            marginBottom: '16px'
                        }}>
                            <div style={{
                                fontSize: '12px',
                                fontWeight: '600',
                                color: '#0369A1',
                                marginBottom: '8px',
                                textTransform: 'uppercase',
                                letterSpacing: '0.5px'
                            }}>
                                Confidence Score
                            </div>
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px'
                            }}>
                                <div style={{
                                    fontSize: '18px',
                                    fontWeight: '700',
                                    color: '#0C4A6E'
                                }}>
                                    {Math.round(selectedColumnData.confidence * 100)}%
                                </div>
                                <div style={{
                                    flex: 1,
                                    height: '6px',
                                    backgroundColor: '#E0F2FE',
                                    borderRadius: '3px',
                                    overflow: 'hidden'
                                }}>
                                    <div style={{
                                        width: `${selectedColumnData.confidence * 100}%`,
                                        height: '100%',
                                        backgroundColor: selectedColumnData.confidence >= 0.8 ? '#10B981' :
                                                       selectedColumnData.confidence >= 0.6 ? '#F59E0B' : '#EF4444',
                                        borderRadius: '3px',
                                        transition: 'width 0.3s ease'
                                    }}></div>
                                </div>
                            </div>
                            <div style={{
                                fontSize: '11px',
                                color: '#0369A1',
                                marginTop: '4px'
                            }}>
                                {selectedColumnData.confidence >= 0.8 ? 'High confidence' :
                                 selectedColumnData.confidence >= 0.6 ? 'Medium confidence' : 'Low confidence'}
                            </div>
                        </div>

                        {/* Used Field Toggle Control */}
                        <div style={{
                            padding: '12px',
                            backgroundColor: '#F8F9FA',
                            border: '1px solid #E9ECEF',
                            borderRadius: '6px',
                            marginBottom: '16px'
                        }}>
                            <div style={{
                                fontSize: '12px',
                                fontWeight: '600',
                                color: '#495057',
                                marginBottom: '8px',
                                textTransform: 'uppercase',
                                letterSpacing: '0.5px'
                            }}>
                                Field Usage
                            </div>
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '12px'
                            }}>
                                <label style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    cursor: 'pointer',
                                    fontSize: '14px',
                                    fontWeight: '500',
                                    color: '#495057'
                                }}>
                                    <input
                                        type="checkbox"
                                        checked={selectedColumnData.used}
                                        onChange={(e) => handleUsedFieldToggle(selectedColumnData, e.target.checked)}
                                        style={{
                                            width: '18px',
                                            height: '18px',
                                            accentColor: '#28A745',
                                            cursor: 'pointer'
                                        }}
                                    />
                                    Use this field
                                </label>
                                <div style={{
                                    fontSize: '12px',
                                    color: selectedColumnData.used ? '#28A745' : '#6C757D',
                                    fontWeight: '600'
                                }}>
                                    {selectedColumnData.used ? '✓ Active' : '○ Inactive'}
                                </div>
                            </div>
                        </div>

                        {/* Developer Information (collapsible) - Hidden by default */}
                        <div style={{
                            marginTop: '20px',
                            padding: '12px',
                            backgroundColor: '#F8FAFC',
                            border: '1px solid #E2E8F0',
                            borderRadius: '6px'
                        }}>
                            <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                marginBottom: showInteractionData ? '12px' : '0'
                            }}>
                                <div style={{
                                    fontSize: '12px',
                                    fontWeight: '600',
                                    color: '#475569',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px'
                                }}>
                                    🔧 Developer Info
                                </div>
                                <button
                                    onClick={() => setShowInteractionData(!showInteractionData)}
                                    style={{
                                        background: 'none',
                                        border: '1px solid #CBD5E1',
                                        borderRadius: '3px',
                                        padding: '2px 6px',
                                        cursor: 'pointer',
                                        fontSize: '11px',
                                        color: '#64748B'
                                    }}
                                >
                                    {showInteractionData ? 'Hide' : 'Show'}
                                </button>
                            </div>

                            {showInteractionData && (
                                <div style={{ display: 'grid', gap: '12px' }}>
                                    {/* Field Information */}
                                    <div style={{
                                        padding: '8px',
                                        backgroundColor: '#F6F8FA',
                                        border: '1px solid #E1E4E8',
                                        borderRadius: '4px'
                                    }}>
                                        <div style={{
                                            fontSize: '11px',
                                            fontWeight: '600',
                                            color: '#586069',
                                            marginBottom: '4px'
                                        }}>
                                            Field Information
                                        </div>
                                        <div style={{ fontSize: '11px', color: '#24292E', marginBottom: '4px' }}>
                                            <strong>Label:</strong> {selectedColumnData.fieldDisplayLabel}
                                        </div>
                                        <div style={{
                                            fontSize: '10px',
                                            color: '#586069',
                                            fontFamily: 'SFMono-Regular, Consolas, monospace',
                                            backgroundColor: '#F1F3F4',
                                            padding: '2px 4px',
                                            borderRadius: '2px',
                                            display: 'inline-block'
                                        }}>
                                            {selectedColumnData.field}
                                        </div>
                                    </div>

                                    {/* Technical Details */}
                                    <div style={{
                                        padding: '8px',
                                        backgroundColor: '#FFFBEB',
                                        border: '1px solid #FED7AA',
                                        borderRadius: '4px'
                                    }}>
                                        <div style={{
                                            fontSize: '11px',
                                            fontWeight: '600',
                                            color: '#92400E',
                                            marginBottom: '4px'
                                        }}>
                                            Technical Details
                                        </div>
                                        <div style={{ display: 'grid', gap: '2px', fontSize: '10px' }}>
                                            <div>
                                                <span style={{ color: '#92400E', fontWeight: '500' }}>Type:</span>{' '}
                                                <span style={{ color: '#451A03' }}>{selectedColumnData.fieldType}</span>
                                            </div>
                                            <div>
                                                <span style={{ color: '#92400E', fontWeight: '500' }}>Data Type:</span>{' '}
                                                <span style={{ color: '#451A03' }}>{selectedColumnData.datatype}</span>
                                            </div>
                                            <div>
                                                <span style={{ color: '#92400E', fontWeight: '500' }}>Status:</span>{' '}
                                                <span style={{
                                                    color: selectedColumnData.used ? '#15803D' : '#737373',
                                                    fontWeight: '600'
                                                }}>
                                                    {selectedColumnData.used ? '✓ Used' : '○ Not used'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Click Data */}
                                    {selectedColumnData.clickTimestamp && (
                                        <div style={{
                                            padding: '8px',
                                            backgroundColor: '#F1F5F9',
                                            border: '1px solid #E2E8F0',
                                            borderRadius: '4px'
                                        }}>
                                            <div style={{ fontSize: '11px', color: '#64748B', marginBottom: '4px' }}>
                                                Click Time: {new Date(selectedColumnData.clickTimestamp).toLocaleString()}
                                            </div>
                                        </div>
                                    )}

                                    {selectedColumnData.clickData && (
                                        <div style={{
                                            padding: '8px',
                                            backgroundColor: '#F1F5F9',
                                            border: '1px solid #E2E8F0',
                                            borderRadius: '4px'
                                        }}>
                                            <div style={{ fontSize: '11px', color: '#64748B', marginBottom: '4px' }}>
                                                Data Attributes:
                                            </div>
                                            <div style={{
                                                fontSize: '9px',
                                                fontFamily: 'SFMono-Regular, Consolas, monospace',
                                                backgroundColor: '#FFFFFF',
                                                padding: '4px',
                                                borderRadius: '2px',
                                                maxHeight: '100px',
                                                overflowY: 'auto',
                                                border: '1px solid #E2E8F0'
                                            }}>
                                                {Object.entries(selectedColumnData.clickData).map(([key, value]) => (
                                                    <div key={key} style={{ marginBottom: '1px' }}>
                                                        <span style={{ color: '#7C3AED', fontWeight: '500' }}>{key}:</span>{' '}
                                                        <span style={{ color: '#1E293B' }}>{String(value)}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </>
                ) : (
                    // Welcome state
                    <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                        <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>
                        <h4 style={{
                            margin: '0 0 8px 0',
                            fontSize: '16px',
                            fontWeight: '600',
                            color: '#24292E'
                        }}>
                            Explore Column Data
                        </h4>
                        <p style={{
                            margin: 0,
                            color: '#586069',
                            fontSize: '14px',
                            lineHeight: '1.5'
                        }}>
                            Click on any column in the diagram to view detailed information about the extracted data.
                        </p>

                        {/* Quick tips */}
                        <div style={{
                            marginTop: '24px',
                            padding: '16px',
                            backgroundColor: '#F6F8FA',
                            borderRadius: '6px',
                            textAlign: 'left'
                        }}>
                            <div style={{
                                fontSize: '12px',
                                fontWeight: '600',
                                color: '#24292E',
                                marginBottom: '8px'
                            }}>
                                💡 Quick Tips:
                            </div>
                            <ul style={{
                                margin: 0,
                                paddingLeft: '16px',
                                fontSize: '12px',
                                color: '#586069',
                                lineHeight: '1.4'
                            }}>
                                <li>Hover over result columns to highlight source documents</li>
                                <li>Used columns are marked with ✓, unused with ○</li>
                                <li>Confidence percentages show extraction accuracy</li>
                                <li>Click any column to view detailed information</li>
                            </ul>
                        </div>
                    </div>
                )}
                </div>
            </div>
        </div>
    );
};

// Helper function to update CRM records for column usage
const updateCRMColumnUsage = async (documentId: number, columns: ColumnData[], context: ComponentFramework.Context<any>) => {
    if (!context) return;
    
    try {
        console.log(`Updating CRM column usage for document ${documentId}`);
        
        // Process each column and update its CRM record
        for (const column of columns) {
            // Find the mapping record ID if available in the column data
            const mappingId = column.id.toString().includes('_')
                ? column.id.toString().split('_')[1]
                : null;

            if (mappingId) {
                // Update the new_used field to the column's used value
                await context.webAPI.updateRecord(
                    "new_ocrdocumentmapping",
                    mappingId,
                    { "new_used": column.used }
                );
                console.log(`Updated mapping ${mappingId} to used=${column.used}`);
            }
        }
    } catch (error) {
        console.error('Error updating CRM column usage:', error);
    }
};

// Helper function to check if running in development environment
const isDevEnvironment = (context?: ComponentFramework.Context<any>) => {
    return !context || window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1';
};


