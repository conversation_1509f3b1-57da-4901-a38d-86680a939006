﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.powerapps.msbuild.solution\1.44.2\build\Microsoft.PowerApps.MSBuild.Solution.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.powerapps.msbuild.solution\1.44.2\build\Microsoft.PowerApps.MSBuild.Solution.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.netframework.referenceassemblies.net462\1.0.0\build\Microsoft.NETFramework.ReferenceAssemblies.net462.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.netframework.referenceassemblies.net462\1.0.0\build\Microsoft.NETFramework.ReferenceAssemblies.net462.targets')" />
  </ImportGroup>
</Project>