{"version": 3, "targets": {".NETFramework,Version=v4.6.2": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.0": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net462": "1.0.0"}}, "Microsoft.NETFramework.ReferenceAssemblies.net462/1.0.0": {"type": "package", "build": {"build/Microsoft.NETFramework.ReferenceAssemblies.net462.targets": {}}}, "Microsoft.PowerApps.MSBuild.Pcf/1.44.2": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.PowerApps.MSBuild.Solution/1.44.2": {"type": "package", "build": {"build/Microsoft.PowerApps.MSBuild.Solution.props": {}, "build/Microsoft.PowerApps.MSBuild.Solution.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.PowerApps.MSBuild.Solution.props": {}, "buildMultiTargeting/Microsoft.PowerApps.MSBuild.Solution.targets": {}}}, "Forton_DocumentOCRUI/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Microsoft.PowerApps.MSBuild.Pcf": "1.0.0"}, "compile": {"bin/placeholder/Forton_DocumentOCRUI.dll": {}}, "runtime": {"bin/placeholder/Forton_DocumentOCRUI.dll": {}}}}}, "libraries": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.0": {"sha512": "7D2TMufjGiowmt0E941kVoTIS+GTNzaPopuzM1/1LSaJAdJdBrVP0SkZW7AgDd0a2U1DjsIeaKG1wxGVBNLDMw==", "type": "package", "path": "microsoft.netframework.referenceassemblies/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.netframework.referenceassemblies.1.0.0.nupkg.sha512", "microsoft.netframework.referenceassemblies.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies.net462/1.0.0": {"sha512": "ONGjkFWduK13lfxUtlEl4+nYwrqDe5NF5f8qRtp5fqWiWYlqft/Ko9ht3e6Secg9y3I1yL8Xnfag/JGOOn0yoQ==", "type": "package", "path": "microsoft.netframework.referenceassemblies.net462/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/.NETFramework/v4.6.2/Accessibility.dll", "build/.NETFramework/v4.6.2/Accessibility.xml", "build/.NETFramework/v4.6.2/CustomMarshalers.dll", "build/.NETFramework/v4.6.2/CustomMarshalers.xml", "build/.NETFramework/v4.6.2/Facades/System.Collections.Concurrent.dll", "build/.NETFramework/v4.6.2/Facades/System.Collections.dll", "build/.NETFramework/v4.6.2/Facades/System.ComponentModel.Annotations.dll", "build/.NETFramework/v4.6.2/Facades/System.ComponentModel.EventBasedAsync.dll", "build/.NETFramework/v4.6.2/Facades/System.ComponentModel.dll", "build/.NETFramework/v4.6.2/Facades/System.Diagnostics.Contracts.dll", "build/.NETFramework/v4.6.2/Facades/System.Diagnostics.Debug.dll", "build/.NETFramework/v4.6.2/Facades/System.Diagnostics.Tools.dll", "build/.NETFramework/v4.6.2/Facades/System.Diagnostics.Tracing.dll", "build/.NETFramework/v4.6.2/Facades/System.Dynamic.Runtime.dll", "build/.NETFramework/v4.6.2/Facades/System.Globalization.dll", "build/.NETFramework/v4.6.2/Facades/System.IO.dll", "build/.NETFramework/v4.6.2/Facades/System.Linq.Expressions.dll", "build/.NETFramework/v4.6.2/Facades/System.Linq.Parallel.dll", "build/.NETFramework/v4.6.2/Facades/System.Linq.Queryable.dll", "build/.NETFramework/v4.6.2/Facades/System.Linq.dll", "build/.NETFramework/v4.6.2/Facades/System.Net.NetworkInformation.dll", "build/.NETFramework/v4.6.2/Facades/System.Net.Primitives.dll", "build/.NETFramework/v4.6.2/Facades/System.Net.Requests.dll", "build/.NETFramework/v4.6.2/Facades/System.Net.WebHeaderCollection.dll", "build/.NETFramework/v4.6.2/Facades/System.ObjectModel.dll", "build/.NETFramework/v4.6.2/Facades/System.Reflection.Emit.ILGeneration.dll", "build/.NETFramework/v4.6.2/Facades/System.Reflection.Emit.Lightweight.dll", "build/.NETFramework/v4.6.2/Facades/System.Reflection.Emit.dll", "build/.NETFramework/v4.6.2/Facades/System.Reflection.Extensions.dll", "build/.NETFramework/v4.6.2/Facades/System.Reflection.Primitives.dll", "build/.NETFramework/v4.6.2/Facades/System.Reflection.dll", "build/.NETFramework/v4.6.2/Facades/System.Resources.ResourceManager.dll", "build/.NETFramework/v4.6.2/Facades/System.Runtime.Extensions.dll", "build/.NETFramework/v4.6.2/Facades/System.Runtime.Handles.dll", "build/.NETFramework/v4.6.2/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "build/.NETFramework/v4.6.2/Facades/System.Runtime.InteropServices.dll", "build/.NETFramework/v4.6.2/Facades/System.Runtime.Numerics.dll", "build/.NETFramework/v4.6.2/Facades/System.Runtime.Serialization.Json.dll", "build/.NETFramework/v4.6.2/Facades/System.Runtime.Serialization.Primitives.dll", "build/.NETFramework/v4.6.2/Facades/System.Runtime.Serialization.Xml.dll", "build/.NETFramework/v4.6.2/Facades/System.Runtime.dll", "build/.NETFramework/v4.6.2/Facades/System.Security.Principal.dll", "build/.NETFramework/v4.6.2/Facades/System.ServiceModel.Duplex.dll", "build/.NETFramework/v4.6.2/Facades/System.ServiceModel.Http.dll", "build/.NETFramework/v4.6.2/Facades/System.ServiceModel.NetTcp.dll", "build/.NETFramework/v4.6.2/Facades/System.ServiceModel.Primitives.dll", "build/.NETFramework/v4.6.2/Facades/System.ServiceModel.Security.dll", "build/.NETFramework/v4.6.2/Facades/System.Text.Encoding.Extensions.dll", "build/.NETFramework/v4.6.2/Facades/System.Text.Encoding.dll", "build/.NETFramework/v4.6.2/Facades/System.Text.RegularExpressions.dll", "build/.NETFramework/v4.6.2/Facades/System.Threading.Tasks.Parallel.dll", "build/.NETFramework/v4.6.2/Facades/System.Threading.Tasks.dll", "build/.NETFramework/v4.6.2/Facades/System.Threading.Timer.dll", "build/.NETFramework/v4.6.2/Facades/System.Threading.dll", "build/.NETFramework/v4.6.2/Facades/System.Xml.ReaderWriter.dll", "build/.NETFramework/v4.6.2/Facades/System.Xml.XDocument.dll", "build/.NETFramework/v4.6.2/Facades/System.Xml.XmlSerializer.dll", "build/.NETFramework/v4.6.2/ISymWrapper.dll", "build/.NETFramework/v4.6.2/ISymWrapper.xml", "build/.NETFramework/v4.6.2/Microsoft.Activities.Build.dll", "build/.NETFramework/v4.6.2/Microsoft.Activities.Build.xml", "build/.NETFramework/v4.6.2/Microsoft.Build.Conversion.v4.0.dll", "build/.NETFramework/v4.6.2/Microsoft.Build.Conversion.v4.0.xml", "build/.NETFramework/v4.6.2/Microsoft.Build.Engine.dll", "build/.NETFramework/v4.6.2/Microsoft.Build.Engine.xml", "build/.NETFramework/v4.6.2/Microsoft.Build.Framework.dll", "build/.NETFramework/v4.6.2/Microsoft.Build.Framework.xml", "build/.NETFramework/v4.6.2/Microsoft.Build.Tasks.v4.0.dll", "build/.NETFramework/v4.6.2/Microsoft.Build.Tasks.v4.0.xml", "build/.NETFramework/v4.6.2/Microsoft.Build.Utilities.v4.0.dll", "build/.NETFramework/v4.6.2/Microsoft.Build.Utilities.v4.0.xml", "build/.NETFramework/v4.6.2/Microsoft.Build.dll", "build/.NETFramework/v4.6.2/Microsoft.Build.xml", "build/.NETFramework/v4.6.2/Microsoft.CSharp.dll", "build/.NETFramework/v4.6.2/Microsoft.CSharp.xml", "build/.NETFramework/v4.6.2/Microsoft.JScript.dll", "build/.NETFramework/v4.6.2/Microsoft.JScript.xml", "build/.NETFramework/v4.6.2/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v4.6.2/Microsoft.VisualBasic.Compatibility.Data.xml", "build/.NETFramework/v4.6.2/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v4.6.2/Microsoft.VisualBasic.Compatibility.xml", "build/.NETFramework/v4.6.2/Microsoft.VisualBasic.dll", "build/.NETFramework/v4.6.2/Microsoft.VisualBasic.xml", "build/.NETFramework/v4.6.2/Microsoft.VisualC.STLCLR.dll", "build/.NETFramework/v4.6.2/Microsoft.VisualC.STLCLR.xml", "build/.NETFramework/v4.6.2/Microsoft.VisualC.dll", "build/.NETFramework/v4.6.2/Microsoft.VisualC.xml", "build/.NETFramework/v4.6.2/PermissionSets/FullTrust.xml", "build/.NETFramework/v4.6.2/PermissionSets/Internet.xml", "build/.NETFramework/v4.6.2/PermissionSets/LocalIntranet.xml", "build/.NETFramework/v4.6.2/PresentationBuildTasks.dll", "build/.NETFramework/v4.6.2/PresentationBuildTasks.xml", "build/.NETFramework/v4.6.2/PresentationCore.dll", "build/.NETFramework/v4.6.2/PresentationCore.xml", "build/.NETFramework/v4.6.2/PresentationFramework.Aero.dll", "build/.NETFramework/v4.6.2/PresentationFramework.Aero.xml", "build/.NETFramework/v4.6.2/PresentationFramework.Aero2.dll", "build/.NETFramework/v4.6.2/PresentationFramework.Aero2.xml", "build/.NETFramework/v4.6.2/PresentationFramework.AeroLite.dll", "build/.NETFramework/v4.6.2/PresentationFramework.AeroLite.xml", "build/.NETFramework/v4.6.2/PresentationFramework.Classic.dll", "build/.NETFramework/v4.6.2/PresentationFramework.Classic.xml", "build/.NETFramework/v4.6.2/PresentationFramework.Luna.dll", "build/.NETFramework/v4.6.2/PresentationFramework.Luna.xml", "build/.NETFramework/v4.6.2/PresentationFramework.Royale.dll", "build/.NETFramework/v4.6.2/PresentationFramework.Royale.xml", "build/.NETFramework/v4.6.2/PresentationFramework.dll", "build/.NETFramework/v4.6.2/PresentationFramework.xml", "build/.NETFramework/v4.6.2/ReachFramework.dll", "build/.NETFramework/v4.6.2/ReachFramework.xml", "build/.NETFramework/v4.6.2/RedistList/FrameworkList.xml", "build/.NETFramework/v4.6.2/System.Activities.Core.Presentation.dll", "build/.NETFramework/v4.6.2/System.Activities.Core.Presentation.xml", "build/.NETFramework/v4.6.2/System.Activities.DurableInstancing.dll", "build/.NETFramework/v4.6.2/System.Activities.DurableInstancing.xml", "build/.NETFramework/v4.6.2/System.Activities.Presentation.dll", "build/.NETFramework/v4.6.2/System.Activities.Presentation.xml", "build/.NETFramework/v4.6.2/System.Activities.dll", "build/.NETFramework/v4.6.2/System.Activities.xml", "build/.NETFramework/v4.6.2/System.AddIn.Contract.dll", "build/.NETFramework/v4.6.2/System.AddIn.Contract.xml", "build/.NETFramework/v4.6.2/System.AddIn.dll", "build/.NETFramework/v4.6.2/System.AddIn.xml", "build/.NETFramework/v4.6.2/System.ComponentModel.Composition.Registration.dll", "build/.NETFramework/v4.6.2/System.ComponentModel.Composition.Registration.xml", "build/.NETFramework/v4.6.2/System.ComponentModel.Composition.dll", "build/.NETFramework/v4.6.2/System.ComponentModel.Composition.xml", "build/.NETFramework/v4.6.2/System.ComponentModel.DataAnnotations.dll", "build/.NETFramework/v4.6.2/System.ComponentModel.DataAnnotations.xml", "build/.NETFramework/v4.6.2/System.Configuration.Install.dll", "build/.NETFramework/v4.6.2/System.Configuration.Install.xml", "build/.NETFramework/v4.6.2/System.Configuration.dll", "build/.NETFramework/v4.6.2/System.Configuration.xml", "build/.NETFramework/v4.6.2/System.Core.dll", "build/.NETFramework/v4.6.2/System.Core.xml", "build/.NETFramework/v4.6.2/System.Data.DataSetExtensions.dll", "build/.NETFramework/v4.6.2/System.Data.DataSetExtensions.xml", "build/.NETFramework/v4.6.2/System.Data.Entity.Design.dll", "build/.NETFramework/v4.6.2/System.Data.Entity.Design.xml", "build/.NETFramework/v4.6.2/System.Data.Entity.dll", "build/.NETFramework/v4.6.2/System.Data.Entity.xml", "build/.NETFramework/v4.6.2/System.Data.Linq.dll", "build/.NETFramework/v4.6.2/System.Data.Linq.xml", "build/.NETFramework/v4.6.2/System.Data.OracleClient.dll", "build/.NETFramework/v4.6.2/System.Data.OracleClient.xml", "build/.NETFramework/v4.6.2/System.Data.Services.Client.dll", "build/.NETFramework/v4.6.2/System.Data.Services.Client.xml", "build/.NETFramework/v4.6.2/System.Data.Services.Design.dll", "build/.NETFramework/v4.6.2/System.Data.Services.Design.xml", "build/.NETFramework/v4.6.2/System.Data.Services.dll", "build/.NETFramework/v4.6.2/System.Data.Services.xml", "build/.NETFramework/v4.6.2/System.Data.SqlXml.dll", "build/.NETFramework/v4.6.2/System.Data.SqlXml.xml", "build/.NETFramework/v4.6.2/System.Data.dll", "build/.NETFramework/v4.6.2/System.Data.xml", "build/.NETFramework/v4.6.2/System.Deployment.dll", "build/.NETFramework/v4.6.2/System.Deployment.xml", "build/.NETFramework/v4.6.2/System.Design.dll", "build/.NETFramework/v4.6.2/System.Design.xml", "build/.NETFramework/v4.6.2/System.Device.dll", "build/.NETFramework/v4.6.2/System.Device.xml", "build/.NETFramework/v4.6.2/System.DirectoryServices.AccountManagement.dll", "build/.NETFramework/v4.6.2/System.DirectoryServices.AccountManagement.xml", "build/.NETFramework/v4.6.2/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v4.6.2/System.DirectoryServices.Protocols.xml", "build/.NETFramework/v4.6.2/System.DirectoryServices.dll", "build/.NETFramework/v4.6.2/System.DirectoryServices.xml", "build/.NETFramework/v4.6.2/System.Drawing.Design.dll", "build/.NETFramework/v4.6.2/System.Drawing.Design.xml", "build/.NETFramework/v4.6.2/System.Drawing.dll", "build/.NETFramework/v4.6.2/System.Drawing.xml", "build/.NETFramework/v4.6.2/System.Dynamic.dll", "build/.NETFramework/v4.6.2/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v4.6.2/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v4.6.2/System.EnterpriseServices.dll", "build/.NETFramework/v4.6.2/System.EnterpriseServices.xml", "build/.NETFramework/v4.6.2/System.IO.Compression.FileSystem.dll", "build/.NETFramework/v4.6.2/System.IO.Compression.FileSystem.xml", "build/.NETFramework/v4.6.2/System.IO.Compression.dll", "build/.NETFramework/v4.6.2/System.IO.Compression.xml", "build/.NETFramework/v4.6.2/System.IO.Log.dll", "build/.NETFramework/v4.6.2/System.IO.Log.xml", "build/.NETFramework/v4.6.2/System.IdentityModel.Selectors.dll", "build/.NETFramework/v4.6.2/System.IdentityModel.Selectors.xml", "build/.NETFramework/v4.6.2/System.IdentityModel.Services.dll", "build/.NETFramework/v4.6.2/System.IdentityModel.Services.xml", "build/.NETFramework/v4.6.2/System.IdentityModel.dll", "build/.NETFramework/v4.6.2/System.IdentityModel.xml", "build/.NETFramework/v4.6.2/System.Linq.xml", "build/.NETFramework/v4.6.2/System.Management.Instrumentation.dll", "build/.NETFramework/v4.6.2/System.Management.Instrumentation.xml", "build/.NETFramework/v4.6.2/System.Management.dll", "build/.NETFramework/v4.6.2/System.Management.xml", "build/.NETFramework/v4.6.2/System.Messaging.dll", "build/.NETFramework/v4.6.2/System.Messaging.xml", "build/.NETFramework/v4.6.2/System.Net.Http.WebRequest.dll", "build/.NETFramework/v4.6.2/System.Net.Http.WebRequest.xml", "build/.NETFramework/v4.6.2/System.Net.Http.dll", "build/.NETFramework/v4.6.2/System.Net.Http.xml", "build/.NETFramework/v4.6.2/System.Net.dll", "build/.NETFramework/v4.6.2/System.Net.xml", "build/.NETFramework/v4.6.2/System.Numerics.dll", "build/.NETFramework/v4.6.2/System.Numerics.xml", "build/.NETFramework/v4.6.2/System.Printing.dll", "build/.NETFramework/v4.6.2/System.Printing.xml", "build/.NETFramework/v4.6.2/System.Reflection.Context.dll", "build/.NETFramework/v4.6.2/System.Reflection.Context.xml", "build/.NETFramework/v4.6.2/System.Runtime.Caching.dll", "build/.NETFramework/v4.6.2/System.Runtime.Caching.xml", "build/.NETFramework/v4.6.2/System.Runtime.DurableInstancing.dll", "build/.NETFramework/v4.6.2/System.Runtime.DurableInstancing.xml", "build/.NETFramework/v4.6.2/System.Runtime.Remoting.dll", "build/.NETFramework/v4.6.2/System.Runtime.Remoting.xml", "build/.NETFramework/v4.6.2/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v4.6.2/System.Runtime.Serialization.Formatters.Soap.xml", "build/.NETFramework/v4.6.2/System.Runtime.Serialization.dll", "build/.NETFramework/v4.6.2/System.Runtime.Serialization.xml", "build/.NETFramework/v4.6.2/System.Security.dll", "build/.NETFramework/v4.6.2/System.Security.xml", "build/.NETFramework/v4.6.2/System.ServiceModel.Activation.dll", "build/.NETFramework/v4.6.2/System.ServiceModel.Activation.xml", "build/.NETFramework/v4.6.2/System.ServiceModel.Activities.dll", "build/.NETFramework/v4.6.2/System.ServiceModel.Activities.xml", "build/.NETFramework/v4.6.2/System.ServiceModel.Channels.dll", "build/.NETFramework/v4.6.2/System.ServiceModel.Channels.xml", "build/.NETFramework/v4.6.2/System.ServiceModel.Discovery.dll", "build/.NETFramework/v4.6.2/System.ServiceModel.Discovery.xml", "build/.NETFramework/v4.6.2/System.ServiceModel.Routing.dll", "build/.NETFramework/v4.6.2/System.ServiceModel.Routing.xml", "build/.NETFramework/v4.6.2/System.ServiceModel.Web.dll", "build/.NETFramework/v4.6.2/System.ServiceModel.Web.xml", "build/.NETFramework/v4.6.2/System.ServiceModel.dll", "build/.NETFramework/v4.6.2/System.ServiceModel.xml", "build/.NETFramework/v4.6.2/System.ServiceProcess.dll", "build/.NETFramework/v4.6.2/System.ServiceProcess.xml", "build/.NETFramework/v4.6.2/System.Speech.dll", "build/.NETFramework/v4.6.2/System.Speech.xml", "build/.NETFramework/v4.6.2/System.Threading.Tasks.Dataflow.xml", "build/.NETFramework/v4.6.2/System.Transactions.dll", "build/.NETFramework/v4.6.2/System.Transactions.xml", "build/.NETFramework/v4.6.2/System.Web.Abstractions.dll", "build/.NETFramework/v4.6.2/System.Web.ApplicationServices.dll", "build/.NETFramework/v4.6.2/System.Web.ApplicationServices.xml", "build/.NETFramework/v4.6.2/System.Web.DataVisualization.Design.dll", "build/.NETFramework/v4.6.2/System.Web.DataVisualization.dll", "build/.NETFramework/v4.6.2/System.Web.DataVisualization.xml", "build/.NETFramework/v4.6.2/System.Web.DynamicData.Design.dll", "build/.NETFramework/v4.6.2/System.Web.DynamicData.Design.xml", "build/.NETFramework/v4.6.2/System.Web.DynamicData.dll", "build/.NETFramework/v4.6.2/System.Web.DynamicData.xml", "build/.NETFramework/v4.6.2/System.Web.Entity.Design.dll", "build/.NETFramework/v4.6.2/System.Web.Entity.Design.xml", "build/.NETFramework/v4.6.2/System.Web.Entity.dll", "build/.NETFramework/v4.6.2/System.Web.Entity.xml", "build/.NETFramework/v4.6.2/System.Web.Extensions.Design.dll", "build/.NETFramework/v4.6.2/System.Web.Extensions.Design.xml", "build/.NETFramework/v4.6.2/System.Web.Extensions.dll", "build/.NETFramework/v4.6.2/System.Web.Extensions.xml", "build/.NETFramework/v4.6.2/System.Web.Mobile.dll", "build/.NETFramework/v4.6.2/System.Web.Mobile.xml", "build/.NETFramework/v4.6.2/System.Web.RegularExpressions.dll", "build/.NETFramework/v4.6.2/System.Web.RegularExpressions.xml", "build/.NETFramework/v4.6.2/System.Web.Routing.dll", "build/.NETFramework/v4.6.2/System.Web.Services.dll", "build/.NETFramework/v4.6.2/System.Web.Services.xml", "build/.NETFramework/v4.6.2/System.Web.dll", "build/.NETFramework/v4.6.2/System.Web.xml", "build/.NETFramework/v4.6.2/System.Windows.Controls.Ribbon.dll", "build/.NETFramework/v4.6.2/System.Windows.Controls.Ribbon.xml", "build/.NETFramework/v4.6.2/System.Windows.Forms.DataVisualization.Design.dll", "build/.NETFramework/v4.6.2/System.Windows.Forms.DataVisualization.dll", "build/.NETFramework/v4.6.2/System.Windows.Forms.DataVisualization.xml", "build/.NETFramework/v4.6.2/System.Windows.Forms.dll", "build/.NETFramework/v4.6.2/System.Windows.Forms.xml", "build/.NETFramework/v4.6.2/System.Windows.Input.Manipulations.dll", "build/.NETFramework/v4.6.2/System.Windows.Input.Manipulations.xml", "build/.NETFramework/v4.6.2/System.Windows.Presentation.dll", "build/.NETFramework/v4.6.2/System.Windows.Presentation.xml", "build/.NETFramework/v4.6.2/System.Windows.dll", "build/.NETFramework/v4.6.2/System.Workflow.Activities.dll", "build/.NETFramework/v4.6.2/System.Workflow.Activities.xml", "build/.NETFramework/v4.6.2/System.Workflow.ComponentModel.dll", "build/.NETFramework/v4.6.2/System.Workflow.ComponentModel.xml", "build/.NETFramework/v4.6.2/System.Workflow.Runtime.dll", "build/.NETFramework/v4.6.2/System.Workflow.Runtime.xml", "build/.NETFramework/v4.6.2/System.WorkflowServices.dll", "build/.NETFramework/v4.6.2/System.WorkflowServices.xml", "build/.NETFramework/v4.6.2/System.Xaml.dll", "build/.NETFramework/v4.6.2/System.Xaml.xml", "build/.NETFramework/v4.6.2/System.Xml.Linq.dll", "build/.NETFramework/v4.6.2/System.Xml.Linq.xml", "build/.NETFramework/v4.6.2/System.Xml.Serialization.dll", "build/.NETFramework/v4.6.2/System.Xml.dll", "build/.NETFramework/v4.6.2/System.Xml.xml", "build/.NETFramework/v4.6.2/System.dll", "build/.NETFramework/v4.6.2/System.xml", "build/.NETFramework/v4.6.2/UIAutomationClient.dll", "build/.NETFramework/v4.6.2/UIAutomationClient.xml", "build/.NETFramework/v4.6.2/UIAutomationClientsideProviders.dll", "build/.NETFramework/v4.6.2/UIAutomationClientsideProviders.xml", "build/.NETFramework/v4.6.2/UIAutomationProvider.dll", "build/.NETFramework/v4.6.2/UIAutomationProvider.xml", "build/.NETFramework/v4.6.2/UIAutomationTypes.dll", "build/.NETFramework/v4.6.2/UIAutomationTypes.xml", "build/.NETFramework/v4.6.2/WindowsBase.dll", "build/.NETFramework/v4.6.2/WindowsBase.xml", "build/.NETFramework/v4.6.2/WindowsFormsIntegration.dll", "build/.NETFramework/v4.6.2/WindowsFormsIntegration.xml", "build/.NETFramework/v4.6.2/XamlBuildTask.dll", "build/.NETFramework/v4.6.2/XamlBuildTask.xml", "build/.NETFramework/v4.6.2/mscorlib.dll", "build/.NETFramework/v4.6.2/mscorlib.xml", "build/.NETFramework/v4.6.2/namespaces.xml", "build/.NETFramework/v4.6.2/sysglobl.dll", "build/.NETFramework/v4.6.2/sysglobl.xml", "build/Microsoft.NETFramework.ReferenceAssemblies.net462.targets", "microsoft.netframework.referenceassemblies.net462.1.0.0.nupkg.sha512", "microsoft.netframework.referenceassemblies.net462.nuspec"]}, "Microsoft.PowerApps.MSBuild.Pcf/1.44.2": {"sha512": "zLDvJPgrteciGxgJgEusxk3QaHkcTT6DASZY6r/gFd1nXgZHcMP/XQFAc+NTqRIaKQjLe5ql+O2UAhfU2fW8nA==", "type": "package", "path": "microsoft.powerapps.msbuild.pcf/1.44.2", "files": [".nupkg.metadata", ".signature.p7s", "3rdPartyNotice.txt", "LICENSE.txt", "build/Microsoft.PowerApps.MSBuild.Pcf.props", "build/Microsoft.PowerApps.MSBuild.Pcf.targets", "microsoft.powerapps.msbuild.pcf.1.44.2.nupkg.sha512", "microsoft.powerapps.msbuild.pcf.nuspec", "tasks/Microsoft.PowerApps.MSBuild.Pcf.tasks", "tasks/net472/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.dll", "tasks/net472/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.pdb", "tasks/net472/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.tasks", "tasks/net472/Newtonsoft.Json.dll", "tasks/net472/System.Drawing.Common.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.ApplicationInsights.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Bcl.AsyncInterfaces.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.DependencyInjection.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.Logging.Abstractions.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.Logging.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.Options.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.Primitives.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net472/ppbtTelemetryRecorder/System.Buffers.dll", "tasks/net472/ppbtTelemetryRecorder/System.Diagnostics.DiagnosticSource.dll", "tasks/net472/ppbtTelemetryRecorder/System.Memory.dll", "tasks/net472/ppbtTelemetryRecorder/System.Numerics.Vectors.dll", "tasks/net472/ppbtTelemetryRecorder/System.Runtime.CompilerServices.Unsafe.dll", "tasks/net472/ppbtTelemetryRecorder/System.Text.Encodings.Web.dll", "tasks/net472/ppbtTelemetryRecorder/System.Text.Json.dll", "tasks/net472/ppbtTelemetryRecorder/System.Threading.Tasks.Extensions.dll", "tasks/net472/ppbtTelemetryRecorder/System.ValueTuple.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/Microsoft.ApplicationInsights.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/Microsoft.Bcl.AsyncInterfaces.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.xml", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Buffers.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Diagnostics.DiagnosticSource.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Memory.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Numerics.Vectors.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Runtime.CompilerServices.Unsafe.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Text.Encodings.Web.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Text.Json.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Threading.Tasks.Extensions.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.ValueTuple.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.exe", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.exe.config", "tasks/net472/ppbtTelemetryRecorder/ppbtTelemetryRecorder.exe", "tasks/net472/ppbtTelemetryRecorder/ppbtTelemetryRecorder.exe.config", "tasks/net472/ppbtTelemetryRecorder/ppbtTelemetryRecorder.pdb", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.deps.json", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.dll", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.pdb", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.tasks", "tasks/net6.0/Microsoft.Win32.SystemEvents.dll", "tasks/net6.0/Newtonsoft.Json.dll", "tasks/net6.0/System.Drawing.Common.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.ApplicationInsights.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.DependencyInjection.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.Logging.Abstractions.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.Logging.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.Options.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.Primitives.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net6.0/ppbtTelemetryRecorder/System.Text.Encodings.Web.dll", "tasks/net6.0/ppbtTelemetryRecorder/System.Text.Json.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/Microsoft.ApplicationInsights.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.xml", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/System.Text.Encodings.Web.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/System.Text.Json.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.deps.json", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.dll.config", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.runtimeconfig.json", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.deps.json", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.dll", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.dll.config", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.pdb", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.runtimeconfig.json", "tasks/net6.0/ppbtTelemetryRecorder/runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "tasks/net6.0/runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll"]}, "Microsoft.PowerApps.MSBuild.Solution/1.44.2": {"sha512": "aYhSSgzZKXCIgq+PUaTlK5lkakTP35hpK6bUlxPfcuGeqmwIAHH3qJMJ72X2pOY3vTIjd+HLEjHcnCkmIKyqMg==", "type": "package", "path": "microsoft.powerapps.msbuild.solution/1.44.2", "files": [".nupkg.metadata", ".signature.p7s", "3rdPartyNotice.txt", "LICENSE.txt", "build/Microsoft.PowerApps.MSBuild.Solution.props", "build/Microsoft.PowerApps.MSBuild.Solution.targets", "buildMultiTargeting/Microsoft.PowerApps.MSBuild.Solution.props", "buildMultiTargeting/Microsoft.PowerApps.MSBuild.Solution.targets", "microsoft.powerapps.msbuild.solution.1.44.2.nupkg.sha512", "microsoft.powerapps.msbuild.solution.nuspec", "tasks/Microsoft.PowerApps.MSBuild.Solution.tasks", "tasks/net472/LibGit2Sharp.dll", "tasks/net472/LibGit2Sharp.dll.config", "tasks/net472/Microsoft.ApplicationInsights.dll", "tasks/net472/Microsoft.Bcl.AsyncInterfaces.dll", "tasks/net472/Microsoft.Deployment.Compression.Cab.dll", "tasks/net472/Microsoft.Deployment.Compression.dll", "tasks/net472/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tasks/net472/Microsoft.Extensions.DependencyInjection.dll", "tasks/net472/Microsoft.Extensions.Logging.Abstractions.dll", "tasks/net472/Microsoft.Extensions.Logging.dll", "tasks/net472/Microsoft.Extensions.Options.dll", "tasks/net472/Microsoft.Extensions.Primitives.dll", "tasks/net472/Microsoft.PowerPlatform.MSBuild.Solution.Tasks.dll", "tasks/net472/Microsoft.PowerPlatform.MSBuild.Solution.Tasks.pdb", "tasks/net472/Microsoft.PowerPlatform.MSBuild.Solution.Tasks.tasks", "tasks/net472/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.dll", "tasks/net472/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.pdb", "tasks/net472/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.tasks", "tasks/net472/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net472/Newtonsoft.Json.dll", "tasks/net472/SolutionPackagerLib.dll", "tasks/net472/System.Buffers.dll", "tasks/net472/System.Configuration.ConfigurationManager.dll", "tasks/net472/System.Diagnostics.DiagnosticSource.dll", "tasks/net472/System.Drawing.Common.dll", "tasks/net472/System.IO.Packaging.dll", "tasks/net472/System.Memory.dll", "tasks/net472/System.Numerics.Vectors.dll", "tasks/net472/System.Runtime.CompilerServices.Unsafe.dll", "tasks/net472/System.Security.AccessControl.dll", "tasks/net472/System.Security.Permissions.dll", "tasks/net472/System.Security.Principal.Windows.dll", "tasks/net472/System.Text.Encodings.Web.dll", "tasks/net472/System.Text.Json.dll", "tasks/net472/System.Threading.Tasks.Extensions.dll", "tasks/net472/System.ValueTuple.dll", "tasks/net472/bolt.system.dll", "tasks/net472/bolt.system.pdb", "tasks/net472/bolt.version.dll", "tasks/net472/bolt.version.pdb", "tasks/net472/bt-uploader/Microsoft.ApplicationInsights.dll", "tasks/net472/bt-uploader/Microsoft.Bcl.AsyncInterfaces.dll", "tasks/net472/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net472/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.xml", "tasks/net472/bt-uploader/System.Buffers.dll", "tasks/net472/bt-uploader/System.Diagnostics.DiagnosticSource.dll", "tasks/net472/bt-uploader/System.Memory.dll", "tasks/net472/bt-uploader/System.Numerics.Vectors.dll", "tasks/net472/bt-uploader/System.Runtime.CompilerServices.Unsafe.dll", "tasks/net472/bt-uploader/System.Text.Encodings.Web.dll", "tasks/net472/bt-uploader/System.Text.Json.dll", "tasks/net472/bt-uploader/System.Threading.Tasks.Extensions.dll", "tasks/net472/bt-uploader/System.ValueTuple.dll", "tasks/net472/bt-uploader/pacTelemetryUpload.exe", "tasks/net472/bt-uploader/pacTelemetryUpload.exe.config", "tasks/net472/lib/linux-arm/libgit2-e632535.so", "tasks/net472/lib/linux-arm64/libgit2-e632535.so", "tasks/net472/lib/linux-musl-arm/libgit2-e632535.so", "tasks/net472/lib/linux-musl-arm64/libgit2-e632535.so", "tasks/net472/lib/linux-musl-x64/libgit2-e632535.so", "tasks/net472/lib/linux-x64/libgit2-e632535.so", "tasks/net472/lib/osx-arm64/libgit2-e632535.dylib", "tasks/net472/lib/osx-x64/libgit2-e632535.dylib", "tasks/net472/lib/win32/arm64/git2-e632535.dll", "tasks/net472/lib/win32/x64/git2-e632535.dll", "tasks/net472/lib/win32/x86/git2-e632535.dll", "tasks/net472/loc/ar/strings.localized.json", "tasks/net472/loc/bg/strings.localized.json", "tasks/net472/loc/ca/strings.localized.json", "tasks/net472/loc/cs/strings.localized.json", "tasks/net472/loc/da/strings.localized.json", "tasks/net472/loc/de/strings.localized.json", "tasks/net472/loc/el/strings.localized.json", "tasks/net472/loc/en/strings.localized.json", "tasks/net472/loc/es/strings.localized.json", "tasks/net472/loc/et/strings.localized.json", "tasks/net472/loc/eu/strings.localized.json", "tasks/net472/loc/fi/strings.localized.json", "tasks/net472/loc/fr/strings.localized.json", "tasks/net472/loc/gl/strings.localized.json", "tasks/net472/loc/he/strings.localized.json", "tasks/net472/loc/hi/strings.localized.json", "tasks/net472/loc/hr/strings.localized.json", "tasks/net472/loc/hu/strings.localized.json", "tasks/net472/loc/id/strings.localized.json", "tasks/net472/loc/it/strings.localized.json", "tasks/net472/loc/ja/strings.localized.json", "tasks/net472/loc/kk/strings.localized.json", "tasks/net472/loc/ko/strings.localized.json", "tasks/net472/loc/lt/strings.localized.json", "tasks/net472/loc/lv/strings.localized.json", "tasks/net472/loc/ms/strings.localized.json", "tasks/net472/loc/nb/strings.localized.json", "tasks/net472/loc/nl/strings.localized.json", "tasks/net472/loc/pl/strings.localized.json", "tasks/net472/loc/pt-br/strings.localized.json", "tasks/net472/loc/pt/strings.localized.json", "tasks/net472/loc/ro/strings.localized.json", "tasks/net472/loc/ru/strings.localized.json", "tasks/net472/loc/sk/strings.localized.json", "tasks/net472/loc/sl/strings.localized.json", "tasks/net472/loc/sr/strings.localized.json", "tasks/net472/loc/sv/strings.localized.json", "tasks/net472/loc/th/strings.localized.json", "tasks/net472/loc/tr/strings.localized.json", "tasks/net472/loc/uk/strings.localized.json", "tasks/net472/loc/vi/strings.localized.json", "tasks/net472/loc/zh-cn/strings.localized.json", "tasks/net472/loc/zh-tw/strings.localized.json", "tasks/net472/loc/zh/strings.localized.json", "tasks/net472/ppbtTelemetryRecorder/Microsoft.ApplicationInsights.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Bcl.AsyncInterfaces.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.DependencyInjection.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.Logging.Abstractions.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.Logging.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.Options.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.Extensions.Primitives.dll", "tasks/net472/ppbtTelemetryRecorder/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net472/ppbtTelemetryRecorder/System.Buffers.dll", "tasks/net472/ppbtTelemetryRecorder/System.Diagnostics.DiagnosticSource.dll", "tasks/net472/ppbtTelemetryRecorder/System.Memory.dll", "tasks/net472/ppbtTelemetryRecorder/System.Numerics.Vectors.dll", "tasks/net472/ppbtTelemetryRecorder/System.Runtime.CompilerServices.Unsafe.dll", "tasks/net472/ppbtTelemetryRecorder/System.Text.Encodings.Web.dll", "tasks/net472/ppbtTelemetryRecorder/System.Text.Json.dll", "tasks/net472/ppbtTelemetryRecorder/System.Threading.Tasks.Extensions.dll", "tasks/net472/ppbtTelemetryRecorder/System.ValueTuple.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/Microsoft.ApplicationInsights.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/Microsoft.Bcl.AsyncInterfaces.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.xml", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Buffers.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Diagnostics.DiagnosticSource.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Memory.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Numerics.Vectors.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Runtime.CompilerServices.Unsafe.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Text.Encodings.Web.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Text.Json.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.Threading.Tasks.Extensions.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/System.ValueTuple.dll", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.exe", "tasks/net472/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.exe.config", "tasks/net472/ppbtTelemetryRecorder/ppbtTelemetryRecorder.exe", "tasks/net472/ppbtTelemetryRecorder/ppbtTelemetryRecorder.exe.config", "tasks/net472/ppbtTelemetryRecorder/ppbtTelemetryRecorder.pdb", "tasks/net6.0/LibGit2Sharp.dll", "tasks/net6.0/Microsoft.ApplicationInsights.dll", "tasks/net6.0/Microsoft.Deployment.Compression.Cab.dll", "tasks/net6.0/Microsoft.Deployment.Compression.dll", "tasks/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tasks/net6.0/Microsoft.Extensions.DependencyInjection.dll", "tasks/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "tasks/net6.0/Microsoft.Extensions.Logging.dll", "tasks/net6.0/Microsoft.Extensions.Options.dll", "tasks/net6.0/Microsoft.Extensions.Primitives.dll", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Solution.Tasks.deps.json", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Solution.Tasks.dll", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Solution.Tasks.pdb", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Solution.Tasks.tasks", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.dll", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.pdb", "tasks/net6.0/Microsoft.PowerPlatform.MSBuild.Telemetry.Tasks.tasks", "tasks/net6.0/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net6.0/Microsoft.Win32.SystemEvents.dll", "tasks/net6.0/Newtonsoft.Json.dll", "tasks/net6.0/SolutionPackagerLib.dll", "tasks/net6.0/System.ComponentModel.Composition.dll", "tasks/net6.0/System.Configuration.ConfigurationManager.dll", "tasks/net6.0/System.Diagnostics.DiagnosticSource.dll", "tasks/net6.0/System.Drawing.Common.dll", "tasks/net6.0/System.IO.Packaging.dll", "tasks/net6.0/System.Security.Cryptography.ProtectedData.dll", "tasks/net6.0/System.Security.Permissions.dll", "tasks/net6.0/System.Text.Encodings.Web.dll", "tasks/net6.0/System.Text.Json.dll", "tasks/net6.0/System.Windows.Extensions.dll", "tasks/net6.0/bolt.system.dll", "tasks/net6.0/bolt.system.pdb", "tasks/net6.0/bolt.version.dll", "tasks/net6.0/bolt.version.pdb", "tasks/net6.0/bt-uploader/Microsoft.ApplicationInsights.dll", "tasks/net6.0/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net6.0/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.xml", "tasks/net6.0/bt-uploader/System.Text.Encodings.Web.dll", "tasks/net6.0/bt-uploader/System.Text.Json.dll", "tasks/net6.0/bt-uploader/pacTelemetryUpload.deps.json", "tasks/net6.0/bt-uploader/pacTelemetryUpload.dll", "tasks/net6.0/bt-uploader/pacTelemetryUpload.dll.config", "tasks/net6.0/bt-uploader/pacTelemetryUpload.runtimeconfig.json", "tasks/net6.0/loc/ar/strings.localized.json", "tasks/net6.0/loc/bg/strings.localized.json", "tasks/net6.0/loc/ca/strings.localized.json", "tasks/net6.0/loc/cs/strings.localized.json", "tasks/net6.0/loc/da/strings.localized.json", "tasks/net6.0/loc/de/strings.localized.json", "tasks/net6.0/loc/el/strings.localized.json", "tasks/net6.0/loc/en/strings.localized.json", "tasks/net6.0/loc/es/strings.localized.json", "tasks/net6.0/loc/et/strings.localized.json", "tasks/net6.0/loc/eu/strings.localized.json", "tasks/net6.0/loc/fi/strings.localized.json", "tasks/net6.0/loc/fr/strings.localized.json", "tasks/net6.0/loc/gl/strings.localized.json", "tasks/net6.0/loc/he/strings.localized.json", "tasks/net6.0/loc/hi/strings.localized.json", "tasks/net6.0/loc/hr/strings.localized.json", "tasks/net6.0/loc/hu/strings.localized.json", "tasks/net6.0/loc/id/strings.localized.json", "tasks/net6.0/loc/it/strings.localized.json", "tasks/net6.0/loc/ja/strings.localized.json", "tasks/net6.0/loc/kk/strings.localized.json", "tasks/net6.0/loc/ko/strings.localized.json", "tasks/net6.0/loc/lt/strings.localized.json", "tasks/net6.0/loc/lv/strings.localized.json", "tasks/net6.0/loc/ms/strings.localized.json", "tasks/net6.0/loc/nb/strings.localized.json", "tasks/net6.0/loc/nl/strings.localized.json", "tasks/net6.0/loc/pl/strings.localized.json", "tasks/net6.0/loc/pt-br/strings.localized.json", "tasks/net6.0/loc/pt/strings.localized.json", "tasks/net6.0/loc/ro/strings.localized.json", "tasks/net6.0/loc/ru/strings.localized.json", "tasks/net6.0/loc/sk/strings.localized.json", "tasks/net6.0/loc/sl/strings.localized.json", "tasks/net6.0/loc/sr/strings.localized.json", "tasks/net6.0/loc/sv/strings.localized.json", "tasks/net6.0/loc/th/strings.localized.json", "tasks/net6.0/loc/tr/strings.localized.json", "tasks/net6.0/loc/uk/strings.localized.json", "tasks/net6.0/loc/vi/strings.localized.json", "tasks/net6.0/loc/zh-cn/strings.localized.json", "tasks/net6.0/loc/zh-tw/strings.localized.json", "tasks/net6.0/loc/zh/strings.localized.json", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.ApplicationInsights.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.DependencyInjection.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.Logging.Abstractions.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.Logging.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.Options.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.Extensions.Primitives.dll", "tasks/net6.0/ppbtTelemetryRecorder/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net6.0/ppbtTelemetryRecorder/System.Text.Encodings.Web.dll", "tasks/net6.0/ppbtTelemetryRecorder/System.Text.Json.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/Microsoft.ApplicationInsights.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/Microsoft.PowerPlatform.Tooling.BatchedTelemetry.xml", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/System.Text.Encodings.Web.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/System.Text.Json.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.deps.json", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.dll", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.dll.config", "tasks/net6.0/ppbtTelemetryRecorder/bt-uploader/pacTelemetryUpload.runtimeconfig.json", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.deps.json", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.dll", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.dll.config", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.pdb", "tasks/net6.0/ppbtTelemetryRecorder/ppbtTelemetryRecorder.runtimeconfig.json", "tasks/net6.0/ppbtTelemetryRecorder/runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "tasks/net6.0/runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "tasks/net6.0/runtimes/linux-arm/native/libgit2-e632535.so", "tasks/net6.0/runtimes/linux-arm64/native/libgit2-e632535.so", "tasks/net6.0/runtimes/linux-musl-arm/native/libgit2-e632535.so", "tasks/net6.0/runtimes/linux-musl-arm64/native/libgit2-e632535.so", "tasks/net6.0/runtimes/linux-musl-x64/native/libgit2-e632535.so", "tasks/net6.0/runtimes/linux-x64/native/libgit2-e632535.so", "tasks/net6.0/runtimes/osx-arm64/native/libgit2-e632535.dylib", "tasks/net6.0/runtimes/osx-x64/native/libgit2-e632535.dylib", "tasks/net6.0/runtimes/win-arm64/native/git2-e632535.dll", "tasks/net6.0/runtimes/win-x64/native/git2-e632535.dll", "tasks/net6.0/runtimes/win-x86/native/git2-e632535.dll", "tasks/net6.0/runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "tasks/net6.0/runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "tasks/net6.0/runtimes/win/lib/net6.0/System.Windows.Extensions.dll"]}, "Forton_DocumentOCRUI/1.0.0": {"type": "project", "path": "../Forton_DocumentOCRUI.pcfproj", "msbuildProject": "../Forton_DocumentOCRUI.pcfproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.6.2": ["Forton_DocumentOCRUI >= 1.0.0", "Microsoft.NETFramework.ReferenceAssemblies >= 1.0.0", "Microsoft.PowerApps.MSBuild.Solution >= 1.*"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\FortonOCRUI\\FortonOCRUI.cdsproj", "projectName": "FortonOCRUI", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\FortonOCRUI\\FortonOCRUI.cdsproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\FortonOCRUI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Downloads": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"targetAlias": "net462", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\Forton_DocumentOCRUI.pcfproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\Forton_DocumentOCRUI.pcfproj"}}}}}, "frameworks": {"net462": {"targetAlias": "net462", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.0, )"}, "Microsoft.PowerApps.MSBuild.Solution": {"target": "Package", "version": "[1.*, )"}}}}}}