/* Main container styles */
.main-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

/* Diagram container */
.diagram-container {
    display: flex;
    width: 100%;
    height: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* GoJS diagram area */
.gojs-diagram {
    flex-grow: 1;
    background-color: #fafafa;
    position: relative;
}

/* Right panel styles */
.right-panel {
    width: 300px;
    background-color: white;
    border-left: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.panel-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* Node detail styles */
.node-detail-section {
    margin-bottom: 16px;
}

.node-detail-label {
    font-weight: 600;
    color: #555;
    margin-bottom: 4px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.node-detail-value {
    color: #333;
    font-size: 14px;
    margin-bottom: 8px;
}

/* Column list styles */
.columns-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #fafafa;
}

.column-item {
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.2s ease;
}

.column-item:last-child {
    border-bottom: none;
}

.column-item.used {
    background-color: #e8f5e8;
    border-left: 4px solid #4caf50;
}

.column-item.unused {
    background-color: #f5f5f5;
    border-left: 4px solid #ccc;
}

.column-item:hover {
    background-color: #f0f0f0;
}

.column-name {
    font-weight: 600;
    color: #333;
    font-size: 13px;
    margin-bottom: 4px;
    line-height: 1.3;
}

.column-field {
    color: #666;
    font-size: 11px;
    font-family: 'Courier New', monospace;
    margin-bottom: 4px;
}

.column-status {
    font-size: 11px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.column-status.used {
    color: #4caf50;
}

.column-status.unused {
    color: #999;
}

.column-status-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.column-status-icon.used {
    background-color: #4caf50;
}

.column-status-icon.unused {
    background-color: #ccc;
}

/* Empty state */
.empty-state {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 32px 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #ddd;
}

/* Toolbar styles */
.diagram-toolbar {
    padding: 8px 16px;
    background-color: white;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    gap: 8px;
    align-items: center;
}

.toolbar-button {
    padding: 6px 12px;
    font-size: 12px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toolbar-button:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.toolbar-button:active {
    background-color: #dee2e6;
}

.toolbar-separator {
    width: 1px;
    height: 20px;
    background-color: #ddd;
    margin: 0 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .diagram-container {
        flex-direction: column;
    }
    
    .right-panel {
        width: 100%;
        height: 300px;
        border-left: none;
        border-top: 1px solid #e0e0e0;
    }
    
    .columns-container {
        max-height: 200px;
    }
}

/* Scrollbar styling */
.panel-content::-webkit-scrollbar,
.columns-container::-webkit-scrollbar {
    width: 6px;
}

.panel-content::-webkit-scrollbar-track,
.columns-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb,
.columns-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.columns-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
