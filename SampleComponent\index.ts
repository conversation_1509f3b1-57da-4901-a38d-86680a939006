import { IInputs, IOutputs } from "./generated/ManifestTypes";
import { createRoot, Root } from 'react-dom/client'; // import the createRoot method and Root type
import React from 'react';
import { OrgChartEditor } from './components/OrgChartEditor';
import { SimpleOrgChart } from './components/SimpleOrgChart';

import './styles/DiagramComponents.css';

export class SampleComponent implements ComponentFramework.StandardControl<IInputs, IOutputs> {
    root: Root; // initialize the root property on the SampleComponent class
    private context: ComponentFramework.Context<IInputs>;
    private _entityId: string | null = null;

    /**
     * Empty constructor.
     */
    constructor() {
        // Empty
    }

    /**
     * Used to initialize the control instance. Controls can kick off remote server calls and other initialization actions here.
     * Data-set values are not initialized here, use updateView.
     * @param context The entire property bag available to control via Context Object; It contains values as set up by the customizer mapped to property names defined in the manifest, as well as utility functions.
     * @param notifyOutputChanged A callback method to alert the framework that the control has new outputs ready to be retrieved asynchronously.
     * @param state A piece of data that persists in one session for a single user. Can be set at any point in a controls life cycle by calling 'setControlState' in the Mode interface.
     * @param container If a control is marked control-type='standard', it will receive an empty div element within which it can render its content.
     */
    public init(
        context: ComponentFramework.Context<IInputs>,
        notifyOutputChanged: () => void,
        state: ComponentFramework.Dictionary,
        container: HTMLDivElement
    ): void {
        // Store context for later use
        this.context = context;

        // Get current OCR header ID if available
        try {
            this._entityId = (context.mode as any).contextInfo?.entityId || null;
            console.log('Current OCR header ID:', this._entityId);
        } catch (error) {
            console.log('Could not get OCR header ID (likely in development mode):', error);
            this._entityId = null;
        }

        // Set container to have 100% height and add main-container class
        container.style.height = "100%";
        container.style.width = "100%";
        container.className = "main-container";

        this.root = createRoot(container); // assign the root React creates to this.root
    }


    /**
     * Called when any value in the property bag has changed. This includes field values, data-sets, global values such as container height and width, offline status, control metadata values such as label, visible, etc.
     * @param context The entire property bag available to control via Context Object; It contains values as set up by the customizer mapped to names defined in the manifest, as well as utility functions
     */
    public updateView(context: ComponentFramework.Context<IInputs>): void {
        // Update stored context
        this.context = context;

        // Update OCR header ID if it has changed
        try {
            const currentEntityId = (context.mode as any).contextInfo?.entityId || null;
            if (currentEntityId !== this._entityId) {
                this._entityId = currentEntityId;
                console.log('OCR header ID updated:', this._entityId);
            }
        } catch (error) {
            // Ignore errors in development mode
        }

        // render the OrgChartEditor component with context and entity ID
        this.root.render(
            React.createElement(OrgChartEditor, {
                width: "100%",
                height: "100%",
                context: this.context,
                entityId: this._entityId
            })
        );
    }

    /**
     * It is called by the framework prior to a control receiving new data.
     * @returns an object based on nomenclature defined in manifest, expecting object[s] for property marked as "bound" or "output"
     */
    public getOutputs(): IOutputs {
        return {};
    }

    /**
     * Called when the control is to be removed from the DOM tree. Controls should use this call for cleanup.
     * i.e. cancelling any pending remote calls, removing listeners, etc.
     */
    public destroy(): void {
        // Add code to cleanup control if necessary
    }
}

// Ensure the control is available in the global namespace for PCF
if (typeof window !== 'undefined') {
    (window as any).SampleNameSpace = (window as any).SampleNameSpace || {};
    (window as any).SampleNameSpace.SampleComponent = SampleComponent;
}
