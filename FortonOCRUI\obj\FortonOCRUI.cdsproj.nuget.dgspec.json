{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\FortonOCRUI\\FortonOCRUI.cdsproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\FortonOCRUI\\FortonOCRUI.cdsproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\FortonOCRUI\\FortonOCRUI.cdsproj", "projectName": "FortonOCRUI", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\FortonOCRUI\\FortonOCRUI.cdsproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\FortonOCRUI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Downloads": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"targetAlias": "net462", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\Forton_DocumentOCRUI.pcfproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\Forton_DocumentOCRUI.pcfproj"}}}}}, "frameworks": {"net462": {"targetAlias": "net462", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.0, )"}, "Microsoft.PowerApps.MSBuild.Solution": {"target": "Package", "version": "[1.*, )"}}}}}, "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\Forton_DocumentOCRUI.pcfproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\Forton_DocumentOCRUI.pcfproj", "projectName": "Forton_DocumentOCRUI", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\Forton_DocumentOCRUI.pcfproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\Forton_DocumentOCRUI - Copy (2)\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Downloads": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"targetAlias": "net462", "projectReferences": {}}}}, "frameworks": {"net462": {"targetAlias": "net462", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.0, )"}, "Microsoft.PowerApps.MSBuild.Pcf": {"target": "Package", "version": "[1.*, )"}}}}}}}