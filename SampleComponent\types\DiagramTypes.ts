export interface ColumnMetadata {
    extractionMethod?: string;
    confidence?: number;
    pageNumber?: number;
    coordinates?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    validationRules?: string[];
    lastModified?: string;
    extractedBy?: string;
    notes?: string;
}

export interface ColumnData {
    id: number;
    fieldDisplayLabel: string;
    valueDisplayLabel: string;
    fieldType: string;
    datatype: string;
    field: string;
    used: boolean;
    confidence: number;
    metadata?: ColumnMetadata;
}

export interface DocumentNodeData {
    key: number;
    name: string;
    type: 'document';
    columns: ColumnData[];
    parent?: number; // The result node this document connects to
}

export interface ResultNodeData {
    key: number;
    name: string;
    type: 'result';
    connectedDocuments?: DocumentNodeData[];
    usedColumns?: ColumnData[];
}

export type NodeData = DocumentNodeData | ResultNodeData;

export interface ConnectionData {
    source: number;
    target: number;
}

export interface DiagramData {
    nodes: NodeData[];
    connections: ConnectionData[];
}

// GoJS specific interfaces
export interface GoJSNodeData {
    key: number;
    name: string;
    type: 'document' | 'result';
    columns?: ColumnData[];
    parent?: number;
    category?: string;
    color?: string;
    usedColumnsCount?: number;
    totalColumnsCount?: number;
    hasMissingRequiredFields?: boolean;
}

export interface GoJSModelData {
    nodeDataArray: GoJSNodeData[];
}
