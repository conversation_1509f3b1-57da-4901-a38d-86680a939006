// Types for our data model
export interface ColumnData {
    id: number;
    fieldDisplayLabel: string;
    valueDisplayLabel: string;
    fieldType: string;
    datatype: string;
    field: string;
    used: boolean;
    confidence: number;
}

// Required fields that must always be visible in result node
export const REQUIRED_FIELDS = [
    { field: 'new_name', displayLabel: 'Name' },
    { field: 'new_assettype', displayLabel: 'Asset Type' },
    { field: 'new_cadasterid', displayLabel: 'Cadaster ID' },
    { field: 'new_propertyuse', displayLabel: 'Property Use' }
];

export interface DocumentData {
    id: number;
    name: string;
    columns?: ColumnData[];
    type?: string;
}

export interface ConnectionData {
    source: number;
    target: number;
}

// Static test data for development
export const testData: DocumentData[] = [
    {
        id: 1,
        name: "Kad SOS 702-707-554-558-pages-1.pdf",
        columns: [
            {
                id: 1,
                fieldDisplayLabel: "Property Type",
                valueDisplayLabel: "Unit",
                fieldType: "OptionSetValue",
                datatype: "varchar",
                field: "new_assettype",
                used: true,
                confidence: 1.00
            }, {
                id: 2,
                fieldDisplayLabel: "Address",
                valueDisplayLabel: "гр. Несебър, п.к. 8230, РОЯЛ БИЙЧ, вх. D, ет. 1, обект 2D",
                fieldType: "string",
                datatype: "varchar",
                field: "address",
                used: true,
                confidence: 0.99

            }, {
                id: 3,
                fieldDisplayLabel: "Cadaster ID",
                valueDisplayLabel: "51500.505.397.1.702",
                fieldType: "string",
                datatype: "varchar",
                field: "new_cadasterid",
                used: true,
                confidence: 0.99
            }
        ]
    },
    {
        id: 2,
        name: "Konstativen NA.pdf",
        columns: [
            {
                id: 4,
                fieldDisplayLabel: "Owner",
                valueDisplayLabel: "ГАЛАКСИ ПРОПЪРТИ ГРУП ООД",
                fieldType: "string",
                datatype: "varchar",
                field: "owner",
                used: true,
                confidence: 0.54
            }, {
                id: 5,
                fieldDisplayLabel: "Name",
                valueDisplayLabel: "Royal Beach Complex",
                fieldType: "string",
                datatype: "varchar",
                field: "new_name",
                used: true,
                confidence: 0.95
            }
        ]
    },
    {
        id: 3,
        name: "Razreshenie za polzvane Royal Beach0001.pdf",
        columns: [
            {
                id: 6,
                fieldDisplayLabel: "address",
                valueDisplayLabel: "УПИ-II , кв. 20 по плана на к.к.  Слънчев бряг-изток, община Несебър",
                fieldType: "string",
                datatype: "varchar",
                field: "address",
                used: false,
                confidence: 0.83
            }, {
                id: 7,
                fieldDisplayLabel: "type",
                valueDisplayLabel: "ХОТЕЛСКИ КОМПЛЕКС",
                fieldType: "string",
                datatype: "varchar",
                field: "type",
                used: false,
                confidence: 0.54
            }, {
                id: 8,
                fieldDisplayLabel: "Property Use",
                valueDisplayLabel: "Hospitality",
                fieldType: "OptionSetValue",
                datatype: "varchar",
                field: "new_propertyuse",
                used: true,
                confidence: 1.00
            }, {
                id: 9,
                fieldDisplayLabel: "Building Type",
                valueDisplayLabel: "Hotel",
                datatype: "varchar",
                fieldType: "OptionSetValue",
                field: "new_buildingtype",
                used: true,
                confidence: 1.00
            }, {
                id: 10,
                fieldDisplayLabel: "Property Type",
                valueDisplayLabel: "Building",
                datatype: "varchar",
                fieldType: "OptionSetValue",
                field: "new_assettype",
                used: true,
                confidence: 1.00
            }
        ]
    },    
    {
        id: 11,
        name: "Result",
        type: "result"
    }
];

export const connections: ConnectionData[] = [
    {
        source: 1,
        target: 11
    },
    {
        source: 2,
        target: 11
    },
    {
        source: 3,
        target: 11
    }
];

// Environment detection
export const isDevEnvironment = (): boolean => {
    // Check if we're in development mode
    // This can be based on various factors like hostname, presence of certain properties, etc.
    if (typeof window !== 'undefined') {
        // Check for localhost or development indicators
        const hostname = window.location.hostname;
        return hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('localhost');
    }
    return false;
};

// CRM Data Service
export class CRMDataService {
    private context: ComponentFramework.Context<any>;
    private entityId: string | null;

    constructor(context: ComponentFramework.Context<any>, entityId: string | null = null) {
        this.context = context;
        this.entityId = entityId;
    }

    // Fetch OCR documents from CRM (for current header if entityId provided)
    async fetchOCRDocuments(): Promise<DocumentData[]> {
        try {
            let documentsResponse;

            if (this.entityId) {
                // Fetch documents for the current OCR header
                console.log('Fetching OCR documents for header:', this.entityId);
                documentsResponse = await this.context.webAPI.retrieveMultipleRecords(
                    "new_ocrdocument",
                    `?$select=new_ocrdocumentid,new_name,_new_ocrheaderid_value&$filter=_new_ocrheaderid_value eq ${this.entityId}`
                );
            } else {
                // Fallback: fetch multiple documents (for development or when entityId is not available)
                console.log('Fetching multiple OCR documents (no specific header)');
                documentsResponse = await this.context.webAPI.retrieveMultipleRecords(
                    "new_ocrdocument",
                    "?$select=new_ocrdocumentid,new_name,_new_ocrheaderid_value&$top=50"
                );
            }

            const documents: DocumentData[] = [];
            let documentId = 1;

            for (const doc of documentsResponse.entities) {
                // Fetch mappings for this document
                const mappingsResponse = await this.context.webAPI.retrieveMultipleRecords(
                    "new_ocrdocumentmapping",
                    `?$select=new_ocrdocumentmappingid,new_displayfieldname,new_displayvalue,new_fieldtype,new_field,new_used,new_confidence,new_value&$filter=_new_ocrdocumentid_value eq ${doc.new_ocrdocumentid}`
                );

                const columns: ColumnData[] = mappingsResponse.entities.map((mapping, index) => ({
                    id: documentId * 1000 + index + 1, // Generate unique ID
                    fieldDisplayLabel: mapping.new_displayfieldname || mapping.new_field || 'Unknown Field',
                    valueDisplayLabel: mapping.new_displayvalue || mapping.new_value || '',
                    fieldType: mapping.new_fieldtype || 'string',
                    datatype: 'varchar', // Default datatype
                    field: mapping.new_field || '',
                    used: mapping.new_used || false,
                    confidence: mapping.new_confidence || 0
                }));

                documents.push({
                    id: documentId++,
                    name: doc.new_name || `Document ${documentId}`,
                    columns: columns
                });
            }

            // Add result node
            documents.push({
                id: documentId,
                name: "Result",
                type: "result"
            });

            return documents;
        } catch (error) {
            console.error('Error fetching CRM data:', error);
            // Fallback to static data on error
            return testData;
        }
    }

    // Generate connections based on documents (all documents connect to result)
    generateConnections(documents: DocumentData[]): ConnectionData[] {
        const resultNode = documents.find(doc => doc.type === 'result');
        if (!resultNode) return [];

        return documents
            .filter(doc => doc.type !== 'result')
            .map(doc => ({
                source: doc.id,
                target: resultNode.id
            }));
    }
}

// Helper function to create complete result columns including required fields
export const createCompleteResultColumns = (usedColumns: ColumnData[]): ColumnData[] => {
    const resultColumns: ColumnData[] = [];
    let nextId = Math.max(...usedColumns.map(col => col.id), 0) + 1000; // Start IDs from a high number to avoid conflicts

    // Add all required fields
    REQUIRED_FIELDS.forEach(requiredField => {
        // Check if this required field exists in used columns
        const existingColumn = usedColumns.find(col => col.field === requiredField.field);

        if (existingColumn) {
            // Use the existing column data
            console.log(`Found existing data for required field ${requiredField.field}:`, existingColumn.valueDisplayLabel);
            resultColumns.push(existingColumn);
        } else {
            // Create a placeholder for missing required field
            console.log(`Creating placeholder for missing required field: ${requiredField.field}`);
            resultColumns.push({
                id: nextId++,
                fieldDisplayLabel: requiredField.displayLabel,
                valueDisplayLabel: '', // Empty value indicates missing
                fieldType: 'string',
                datatype: 'varchar',
                field: requiredField.field,
                used: false, // Mark as not used since it's missing
                confidence: 0
            });
        }
    });

    // Add any other used columns that are not in required fields
    usedColumns.forEach(col => {
        const isRequired = REQUIRED_FIELDS.some(req => req.field === col.field);
        if (!isRequired) {
            resultColumns.push(col);
        }
    });

    return resultColumns;
};

// Helper function to check if result node has missing required fields
export const hasMissingRequiredFields = (columns: ColumnData[]): boolean => {
    return REQUIRED_FIELDS.some(requiredField => {
        const column = columns.find(col => col.field === requiredField.field);
        return !column || !column.valueDisplayLabel || column.valueDisplayLabel.trim() === '';
    });
};

// Helper function to update result node data based on connected documents
export const updateResultNodeData = (
    connectedDocumentKeys: number[],
    allDocuments: DocumentData[],
    newlyConnectedDocumentKey?: number,
    existingResultColumns?: ColumnData[]
): { columns: ColumnData[], hasMissingFields: boolean, updatedDocuments?: DocumentData[] } => {
    // Get connected documents
    const connectedDocs = allDocuments.filter(doc =>
        connectedDocumentKeys.includes(doc.id) && doc.type !== 'result'
    );

    // Auto-activate fields for newly connected documents
    let updatedDocuments: DocumentData[] = [];
    if (newlyConnectedDocumentKey && existingResultColumns) {
        const newlyConnectedDoc = connectedDocs.find(doc => doc.id === newlyConnectedDocumentKey);
        if (newlyConnectedDoc && newlyConnectedDoc.columns) {
            // Check if all fields in the newly connected document are unused
            const allFieldsUnused = newlyConnectedDoc.columns.every(col => !col.used);

            if (allFieldsUnused) {
                console.log('Auto-activating fields for newly connected document:', newlyConnectedDoc.name);

                // Get existing fields from result node with their values
                const existingFieldsMap = new Map(
                    existingResultColumns.map(col => [col.field, col])
                );

                // Auto-activate fields that don't exist OR exist but are empty placeholders
                const updatedColumns = newlyConnectedDoc.columns.map(col => {
                    const existingField = existingFieldsMap.get(col.field);

                    // Activate if field doesn't exist OR if it exists but is empty (placeholder)
                    const shouldActivate = !existingField ||
                                          !existingField.valueDisplayLabel ||
                                          existingField.valueDisplayLabel.trim() === '';

                    if (shouldActivate) {
                        console.log(`Auto-activating field: ${col.field} (${col.fieldDisplayLabel}) - ${existingField ? 'replacing empty placeholder' : 'new field'}`);
                        return { ...col, used: true };
                    } else {
                        console.log(`Skipping field: ${col.field} - already has value: "${existingField.valueDisplayLabel}"`);
                    }
                    return col;
                });

                // Update the document in our working copy
                const updatedDoc = { ...newlyConnectedDoc, columns: updatedColumns };
                updatedDocuments = [updatedDoc];

                // Replace the document in connectedDocs for processing
                const docIndex = connectedDocs.findIndex(doc => doc.id === newlyConnectedDocumentKey);
                if (docIndex !== -1) {
                    connectedDocs[docIndex] = updatedDoc;
                }
            }
        }
    }

    // Get all used columns from connected documents with source info
    const usedColumns = connectedDocs.flatMap(doc =>
        (doc.columns?.filter(col => col.used) || []).map(col => ({
            ...col,
            sourceDocumentId: doc.id,
            sourceDocumentName: doc.name
        }))
    );

    console.log('Used columns from connected documents:', usedColumns.map(col => `${col.field}: "${col.valueDisplayLabel}" (used: ${col.used})`));

    // Create complete result columns including required fields
    const completeResultColumns = createCompleteResultColumns(usedColumns);

    // Check if any required fields are missing
    const hasMissingFields = hasMissingRequiredFields(completeResultColumns);

    return {
        columns: completeResultColumns,
        hasMissingFields,
        updatedDocuments
    };
};

// Main data loading function
export const loadData = async (context?: ComponentFramework.Context<any>, entityId?: string | null): Promise<{ data: DocumentData[], connections: ConnectionData[] }> => {
    if (isDevEnvironment() || !context) {
        console.log('Using static test data (development mode)');
        return {
            data: testData,
            connections: connections
        };
    } else {
        console.log('Fetching live data from CRM', entityId ? `for OCR header: ${entityId}` : '(no specific header)');
        const crmService = new CRMDataService(context, entityId);
        const data = await crmService.fetchOCRDocuments();
        const connections = crmService.generateConnections(data);
        return {
            data,
            connections
        };
    }
};
