<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Right Side Panel Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .demo-container {
            width: 800px;
            height: 600px;
            border: 2px solid #007acc;
            position: relative;
            background-color: #F9FAFB;
            margin: 0 auto;
        }
        .right-panel {
            position: absolute;
            top: 50px;
            right: 10px;
            bottom: 50px;
            background: white;
            color: #374151;
            border-radius: 4px;
            border: 1px solid #D1D5DB;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease-in-out;
            z-index: 10;
        }
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #E5E7EB;
            padding-bottom: 8px;
        }
        .panel-content {
            display: flex;
            flex-direction: column;
            gap: 12px;
            flex: 1;
            overflow-y: auto;
        }
        .section-title {
            margin: 0 0 8px 0;
            font-size: 12px;
            color: #6B7280;
            text-transform: uppercase;
        }
        .button {
            padding: 4px 8px;
            font-size: 11px;
            background-color: #F3F4F6;
            border: 1px solid #D1D5DB;
            border-radius: 3px;
            cursor: pointer;
            width: 100%;
            text-align: left;
        }
        .button:hover {
            background-color: #E5E7EB;
        }
        .toggle-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            padding: 4px;
            border-radius: 2px;
            color: #6B7280;
            transition: all 0.2s ease-in-out;
        }
        .toggle-btn:hover {
            background-color: #F3F4F6;
        }
        .minimized {
            width: 40px !important;
            padding: 8px !important;
        }
        .expanded {
            width: 250px;
            padding: 12px;
        }
    </style>
</head>
<body>
    <h1>Right Side Panel Demo</h1>
    <p>This demonstrates the new minimizable right side panel functionality.</p>
    
    <div class="demo-container">
        <!-- Simulated diagram area -->
        <div style="position: absolute; top: 20px; left: 20px; background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
            📊 Main Diagram Area
        </div>
        
        <!-- Right Side Panel -->
        <div id="rightPanel" class="right-panel expanded">
            <!-- Panel Header -->
            <div class="panel-header" id="panelHeader">
                <span id="panelTitle" style="font-size: 14px; font-weight: bold;">
                    📊 Control Panel
                </span>
                <button class="toggle-btn" onclick="togglePanel()" title="Minimize panel">
                    ➖
                </button>
            </div>

            <!-- Panel Content -->
            <div id="panelContent" class="panel-content">
                <!-- Diagram Controls Section -->
                <div>
                    <h4 class="section-title">Diagram Controls</h4>
                    <div style="display: flex; flex-direction: column; gap: 6px;">
                        <button class="button" onclick="alert('Refresh Connections clicked!')">
                            🔄 Refresh Connections
                        </button>
                        <button class="button" onclick="alert('Center View clicked!')">
                            🎯 Center View
                        </button>
                    </div>
                </div>

                <!-- Statistics Section -->
                <div>
                    <h4 class="section-title">Statistics</h4>
                    <div style="font-size: 11px; color: #374151; line-height: 1.4;">
                        <div>📄 Documents: 3</div>
                        <div>🔗 Connections: Active</div>
                        <div>⚡ Status: Ready</div>
                    </div>
                </div>

                <!-- Tools Section -->
                <div>
                    <h4 class="section-title">Tools</h4>
                    <div style="display: flex; flex-direction: column; gap: 6px;">
                        <button class="button" onclick="alert('Test Highlighting clicked!')">
                            🎨 Test Highlighting
                        </button>
                        <button class="button" onclick="alert('Debug Info clicked!')">
                            🔧 Debug Info
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isMinimized = false;
        
        function togglePanel() {
            const panel = document.getElementById('rightPanel');
            const header = document.getElementById('panelHeader');
            const content = document.getElementById('panelContent');
            const title = document.getElementById('panelTitle');
            const button = document.querySelector('.toggle-btn');
            
            isMinimized = !isMinimized;
            
            if (isMinimized) {
                panel.className = 'right-panel minimized';
                content.style.display = 'none';
                title.style.display = 'none';
                header.style.borderBottom = 'none';
                header.style.paddingBottom = '0';
                header.style.marginBottom = '0';
                button.innerHTML = '➕';
                button.title = 'Expand panel';
            } else {
                panel.className = 'right-panel expanded';
                content.style.display = 'flex';
                title.style.display = 'block';
                header.style.borderBottom = '1px solid #E5E7EB';
                header.style.paddingBottom = '8px';
                header.style.marginBottom = '12px';
                button.innerHTML = '➖';
                button.title = 'Minimize panel';
            }
        }
    </script>
</body>
</html>
