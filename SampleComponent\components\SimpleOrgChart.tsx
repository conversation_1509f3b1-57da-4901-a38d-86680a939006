import React, { useEffect, useRef } from 'react';
import * as go from 'gojs';

interface SimpleOrgChartProps {
    width: string;
    height: string;
}

export const SimpleOrgChart: React.FC<SimpleOrgChartProps> = ({ width, height }) => {
    const diagramRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!diagramRef.current) return;

        const $ = go.GraphObject.make;

        // Create a simple diagram
        const diagram = $(go.Diagram, diagramRef.current, {
            'undoManager.isEnabled': true,
            layout: $(go.TreeLayout, { angle: 90, layerSpacing: 50 })
        });

        // Define a simple node template
        diagram.nodeTemplate =
            $(go.Node, 'Auto',
                $(go.Shape, 'RoundedRectangle',
                    { fill: 'lightblue', stroke: 'blue' }
                ),
                $(go.TextBlock,
                    { margin: 8, font: 'bold 12px sans-serif' },
                    new go.Binding('text', 'name')
                )
            );

        // Create simple test data
        const nodeDataArray = [
            { key: 1, name: 'Document 1', parent: 4 },
            { key: 2, name: 'Document 2', parent: 4 },
            { key: 3, name: 'Document 3', parent: 4 },
            { key: 4, name: 'Result Node' }
        ];

        diagram.model = new go.TreeModel(nodeDataArray);

        return () => {
            diagram.div = null;
        };
    }, []);

    return (
        <div style={{ width, height, border: '1px solid #ccc' }}>
            <div ref={diagramRef} style={{ width: '100%', height: '100%' }} />
        </div>
    );
};
