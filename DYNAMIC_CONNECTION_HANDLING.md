# Dynamic Connection Handling Feature

## Overview

The OCR Document UI component now includes **Dynamic Connection Handling** that automatically updates required fields in result nodes when connections are created or deleted in real-time.

## Key Features

### 🔗 Connection Creation
When a document node is connected to a result node:
- **Automatic Field Detection**: Scans the newly connected document for required fields
- **Smart Field Activation**: If all document fields are unused, automatically activates fields that don't exist in the result node
- **Data Integration**: Adds available field values to the result node
- **Visual Update**: Updates result node appearance based on field completeness
- **Real-time Refresh**: Changes appear immediately without page refresh
- **CRM Synchronization**: Updates live CRM records when fields are activated/deactivated

### ❌ Connection Deletion  
When a document node is disconnected from a result node:
- **Field Removal**: Removes field values that were sourced from the disconnected document
- **Missing Field Detection**: Marks required fields as "Missing" if no longer available
- **Visual Feedback**: Updates result node to show reddish background if fields are missing
- **Preserved Data**: Keeps field values from other still-connected documents

### 🎯 Smart Field Activation
When connecting a document with all unused fields:
- **Conflict Detection**: Checks if document fields already exist in the result node
- **Auto-Activation**: Automatically sets `used: true` for non-conflicting fields
- **Visual Update**: Document node immediately shows activated fields
- **CRM Integration**: Updates live CRM records with new field usage status
- **Intelligent Merging**: Prevents duplicate fields while maximizing data utilization

### 🎨 Visual Updates
- **Green Result Node**: All required fields have values from connected documents
- **Reddish Result Node**: One or more required fields are missing
- **Dynamic Borders**: Border color changes based on field completeness
- **Immediate Feedback**: Visual changes happen instantly when connections change

## Implementation Details

### Event Listeners

The component listens for multiple GoJS events to catch all connection changes:

```typescript
// 1. Model Changes (TreeModel parent relationships)
myDiagram.addModelChangedListener((e) => {
    if (e.change === go.ChangeType.Property && e.propertyName === "parent") {
        // Handle connection creation/deletion
        updateResultNodeColumns(resultNodeKey);
    }
});

// 2. Selection Deletion
myDiagram.addDiagramListener("SelectionDeleted", (e) => {
    // Handle when users delete connections via selection
});

// 3. Link Drawing
myDiagram.addDiagramListener("LinkDrawn", (e) => {
    // Handle when users draw new connections
});

// 4. Link Relinking
myDiagram.addDiagramListener("LinkRelinked", (e) => {
    // Handle when users move connection endpoints
});
```

### Data Processing

```typescript
// Helper function for updating result node data
export const updateResultNodeData = (
    connectedDocumentKeys: number[], 
    allDocuments: DocumentData[]
): { columns: ColumnData[], hasMissingFields: boolean } => {
    // Get connected documents
    const connectedDocs = allDocuments.filter(doc => 
        connectedDocumentKeys.includes(doc.id) && doc.type !== 'result'
    );
    
    // Extract used columns with source information
    const usedColumns = connectedDocs.flatMap(doc =>
        (doc.columns?.filter(col => col.used) || []).map(col => ({
            ...col,
            sourceDocumentId: doc.id,
            sourceDocumentName: doc.name
        }))
    );

    // Create complete result columns including required fields
    const completeResultColumns = createCompleteResultColumns(usedColumns);
    
    // Check for missing required fields
    const hasMissingFields = hasMissingRequiredFields(completeResultColumns);
    
    return { columns: completeResultColumns, hasMissingFields };
};
```

### Visual Property Updates

```typescript
// Update result node properties based on field completeness
model.setDataProperty(resultNodeData, "columns", resultData.columns);
model.setDataProperty(resultNodeData, "hasMissingRequiredFields", resultData.hasMissingFields);

// Dynamic color based on missing fields
const resultColor = resultData.hasMissingFields ? '#E8B4B4' : '#4CAF50';
model.setDataProperty(resultNodeData, "color", resultColor);
```

## User Experience

### Scenario 1: Adding Connections
1. User drags from document node to result node
2. **Instant Update**: Result node immediately shows new field values
3. **Visual Feedback**: Background changes from red to green if all required fields are now complete
4. **Field Display**: New fields appear in the result node column list

### Scenario 2: Removing Connections
1. User deletes a connection between document and result node
2. **Immediate Response**: Fields sourced from that document are removed
3. **Missing Field Indication**: Required fields show "Missing" if no other documents provide them
4. **Visual Warning**: Result node background becomes reddish if required fields are missing

### Scenario 3: Reconnecting
1. User moves a connection from one result node to another
2. **Dual Update**: Both old and new result nodes update their field lists
3. **Accurate State**: Each result node shows only fields from its connected documents
4. **Preserved Positions**: Node positions remain stable during updates

## Technical Benefits

### 🚀 Performance
- **Efficient Updates**: Only affected result nodes are recalculated
- **Position Preservation**: Node layouts remain stable during updates
- **Single Transactions**: All changes happen in one atomic operation

### 🔄 Real-time Synchronization
- **Immediate Feedback**: No delays or manual refresh needed
- **Consistent State**: Diagram always reflects current connection state
- **Multiple Event Handling**: Catches all ways users can modify connections

### 🛡️ Robustness
- **Error Handling**: Graceful fallback if updates fail
- **Transaction Safety**: Changes are rolled back on errors
- **Layout Preservation**: Original layout restored even on failures

## Configuration

### Required Fields
The system automatically tracks these required fields:
- `new_name` - Name
- `new_assettype` - Asset Type  
- `new_cadasterid` - Cadaster ID
- `new_propertyuse` - Property Use

### Customization
To modify which fields are considered required, update the `REQUIRED_FIELDS` array in `dataModelUtils.ts`.

## Testing

### Interactive Testing
1. **Connect Documents**: Drag from document nodes to result node
2. **Observe Changes**: Watch result node update field list and colors
3. **Disconnect Documents**: Delete connections and see missing field indicators
4. **Reconnect**: Move connections between result nodes

### Expected Behavior
- ✅ Result node shows all available required fields
- ✅ Missing required fields display "Missing" in red
- ✅ Background color reflects field completeness
- ✅ Changes happen immediately without lag
- ✅ Node positions remain stable during updates

This feature ensures that users always have accurate, up-to-date information about field completeness as they modify document connections in the diagram interface.
