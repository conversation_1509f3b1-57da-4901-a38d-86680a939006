# Required Fields Feature

## Overview

The OCR Document UI component now includes a **Required Fields** feature that ensures critical fields are always visible in the result node, even if they're missing from the source documents.

## Required Fields

The following fields are now **always displayed** in the result node:

1. **`new_name`** - Name
2. **`new_assettype`** - Asset Type  
3. **`new_cadasterid`** - Cadaster ID
4. **`new_propertyuse`** - Property Use

## Visual Indicators

### Result Node Background
- **🟢 Green Background** (`#E8F5E8`): All required fields have values
- **🔴 Light Red Background** (`#F5E6E6`): One or more required fields are missing

### Result Node Border
- **🟢 Green Border** (`#4CAF50`): All required fields complete
- **🔴 Red Border** (`#D32F2F`): Missing required fields

### Column Indicators
- **✓ Green Checkmark**: Field has a value
- **⚠ Orange Warning**: Required field is missing
- **Red Text**: Missing field values show "Missing" in red

## Implementation Details

### Data Processing
```typescript
// Required fields configuration
export const REQUIRED_FIELDS = [
    { field: 'new_name', displayLabel: 'Name' },
    { field: 'new_assettype', displayLabel: 'Asset Type' },
    { field: 'new_cadasterid', displayLabel: 'Cadaster ID' },
    { field: 'new_propertyuse', displayLabel: 'Property Use' }
];

// Creates complete result columns including missing required fields
const completeResultColumns = createCompleteResultColumns(usedColumns);

// Checks if any required fields are missing
const hasMissingFields = hasMissingRequiredFields(completeResultColumns);
```

### Visual Styling
```typescript
// Dynamic background color based on missing fields
const resultColor = hasMissingFields ? '#E8B4B4' : '#4CAF50';

// Column text styling for missing values
const displayValue = hasValue ? column.valueDisplayLabel : 'Missing';
const textColor = hasValue ? '#333' : '#D32F2F';
```

## User Experience

### Complete Data Scenario
When all required fields are present:
- Result node has green background and border
- All columns show green checkmarks
- Field values are displayed normally

### Missing Data Scenario  
When required fields are missing:
- Result node has subtle reddish background and red border
- Missing fields show orange warning icons
- Missing field values display "Missing" in red text
- Fields are still clickable for interaction

## Legend Updates

The component legend now includes:
- **⚠️ Missing Fields**: Result node with missing required fields
- **⚠ Missing Required**: Required field with no value

## Benefits

1. **🎯 Data Completeness**: Users immediately see what data is missing
2. **🔍 Visual Clarity**: Clear distinction between complete and incomplete data
3. **📋 Compliance**: Ensures critical fields are never overlooked
4. **🚀 Workflow**: Guides users to complete required information

## Testing

### Development Mode
The test data includes:
- **Complete fields**: `new_name`, `new_assettype`, `new_cadasterid`, `new_propertyuse`
- **Missing field**: `new_propertyuse` is missing from some documents
- This demonstrates both complete and incomplete scenarios

### Production Mode
When deployed to CRM:
- Fetches actual field values from `new_ocrdocumentmapping`
- Automatically detects missing required fields
- Applies visual indicators based on real data

## Configuration

To modify required fields, update the `REQUIRED_FIELDS` array in `dataModelUtils.ts`:

```typescript
export const REQUIRED_FIELDS = [
    { field: 'your_field_name', displayLabel: 'Your Display Label' },
    // Add more required fields as needed
];
```

## Deployment

The feature is included in the latest build and ready for deployment:
- **File**: `out/controls/SampleComponent/bundle.js`
- **Size**: 1.89 MB
- **Status**: ✅ Build successful

This enhancement ensures data quality and provides clear visual feedback about the completeness of extracted OCR data.
