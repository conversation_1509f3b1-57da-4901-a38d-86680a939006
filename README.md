# Svelvet PCF Component

This project demonstrates how to integrate Svelvet diagram library with React 19 in Power Apps Component Framework (PCF) components. It provides an interactive, feature-rich diagram component with automatic layout, pan/zoom, and drag-and-drop capabilities.

## What's Included

- **Svelvet Integration** - Advanced diagram library with automatic layout
- **React 19.1.0** - Latest version of React with modern features
- **TypeScript Support** - Full TypeScript integration with React JSX transform
- **PCF Standard Control** - Non-virtualized PCF component for maximum React compatibility
- **Interactive Features** - Pan, zoom, drag nodes, automatic layout
- **Hot Reload** - Development server with automatic browser refresh

## Project Structure

```
├── SampleComponent/
│   ├── index.ts                      # Main PCF component class
│   ├── CommunityDiagramComponent.tsx # Svelvet diagram React component
│   ├── utils/
│   │   └── dataModelUtils.ts         # Data model utilities and sample data
│   ├── styles/
│   │   └── DiagramStyles.ts          # Comprehensive diagram styling
│   ├── SvelteWrapper.ts              # Alternative Svelte wrapper (legacy)
│   └── ControlManifest.Input.xml     # PCF manifest
├── package.json                      # Dependencies including Svelvet & React 19
├── tsconfig.json                     # TypeScript config with react-jsx
└── README.md
```

## Key Features Demonstrated

1. **Svelvet Integration** - Advanced diagram library with automatic layout algorithms
2. **Interactive Diagrams** - Drag nodes, pan canvas, zoom in/out
3. **Automatic Layout** - Intelligent positioning of nodes by type (Document → Process → Result)
4. **React 19 Integration** - Using `createRoot` from `react-dom/client`
5. **Modern JSX Transform** - Configured with `"jsx": "react-jsx"`
6. **TypeScript Support** - Full type safety for diagram components
7. **PCF Lifecycle** - Proper integration with init() and updateView()
8. **Hot Reload** - Live development with automatic updates
9. **Responsive Design** - Works on desktop and mobile devices
10. **Accessibility** - Keyboard navigation and screen reader support

## Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run start:watch
   ```

3. **Open browser:**
   Navigate to http://localhost:8181

## Svelvet Diagram Features

### Interactive Controls
- **🔄 Auto Layout** - Automatically arranges nodes in a hierarchical layout
- **🎯 Fit to View** - Centers and resets the diagram view
- **🔍 Zoom Controls** - Zoom in/out with buttons or mouse wheel
- **🖱️ Pan & Drag** - Click and drag to pan the canvas, drag nodes to reposition

### Node Types
- **📄 Document Nodes** - Input documents (invoices, receipts, contracts)
- **⚙️ Process Nodes** - Processing steps (OCR, validation, extraction)
- **✅ Result Nodes** - Output results (extracted data, validation reports)

### Visual Features
- **Animated Connections** - Smooth curved lines with arrow markers
- **Status Indicators** - Color-coded status (active, processing, inactive)
- **Hover Effects** - Interactive feedback on nodes and connections
- **Responsive Layout** - Adapts to different screen sizes

### Data Model
- **Sample Data** - Pre-configured with document processing workflow
- **Extensible** - Easy to add new node types and connections
- **Type Safety** - Full TypeScript support for all data structures

## Development Commands

- `npm run build` - Build the component
- `npm run start:watch` - Start with hot reload
- `npm run lint` - Run ESLint
- `npm run clean` - Clean build outputs

## Technology Stack

### Core Technologies
- **Svelvet 11.0.5** - Advanced diagram library with automatic layout
- **React 19.1.0** - Latest React with improved performance
- **TypeScript 5.8.3** - Type safety and modern JavaScript features
- **PCF Framework** - Power Apps Component Framework integration

### React 19 Benefits
- **Improved Performance** - Better rendering optimizations
- **Modern JSX Transform** - No need to import React in every file
- **Enhanced Developer Experience** - Better error messages and debugging
- **Future-Ready** - Access to latest React features and patterns

## Implementation Notes

- This setup uses a **standard control** (not React virtual control) to avoid React version limitations
- **Virtualization is disabled** but this trade-off allows modern React features
- The component works in both Canvas and Model-Driven apps
- Compatible with Power Platform deployment
- **Svelvet integration** is achieved through a React wrapper component
- **Automatic layout** uses a simplified Dagre-like algorithm for node positioning

## Customization

### Adding New Node Types
1. Update the `DiagramNode` interface in `utils/dataModelUtils.ts`
2. Add styling in `styles/DiagramStyles.ts`
3. Update the `createNodeElement` function in `CommunityDiagramComponent.tsx`

### Modifying Layout Algorithm
- Edit the `applyEnhancedAutoLayout` function to change positioning logic
- Adjust spacing, grouping, or hierarchy rules

### Styling Customization
- Modify `styles/DiagramStyles.ts` for visual appearance
- Update CSS classes for nodes, connections, and controls
- Add dark mode or theme variations

## Next Steps

- Integrate with real data sources (SharePoint, Dataverse)
- Add more node types and connection styles
- Implement advanced layout algorithms (force-directed, hierarchical)
- Add export functionality (PNG, SVG, PDF)
- Create unit tests with React Testing Library
- Deploy to Power Platform environment
- Add real-time collaboration features
