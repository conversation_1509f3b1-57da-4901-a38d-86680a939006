# Conditional Data Sourcing Implementation

## Overview

This implementation provides conditional data sourcing for the OCR Document UI component:
- **Development Environment**: Uses static test data from `dataModelUtils.ts`
- **Production/CRM Environment**: Fetches live data from Dynamics 365 CRM using `context.webAPI`

## Key Features

### Environment Detection
- Automatically detects if running in development mode (localhost)
- Falls back to static data if CRM API calls fail

### CRM Integration
- **Smart Document Loading**: Fetches OCR documents for the current header when deployed in CRM
- Gets current OCR header ID using `context.mode.contextInfo.entityId`
- Filters documents by `new_ocrheaderid` to get all documents in the current header
- Fetches data from `new_ocrdocument` and `new_ocrdocumentmapping` entities
- Uses only the necessary fields for optimal performance
- Handles errors gracefully with fallback to static data

### Data Transformation
- Converts CRM data to the expected component format
- Maintains compatibility with existing UI components
- Generates connections automatically based on document relationships

## Implementation Details

### Files Modified

1. **`SampleComponent/utils/dataModelUtils.ts`**
   - Added TypeScript interfaces for type safety
   - Created `CRMDataService` class for CRM API interactions
   - Implemented `loadData()` function for conditional data loading
   - Added environment detection logic

2. **`SampleComponent/index.ts`**
   - Added context storage and passing to React components
   - **OCR Header ID Detection**: Gets current OCR header ID using `context.mode.contextInfo.entityId`
   - Passes both context and header ID to React components
   - Updated component initialization to pass CRM context

3. **`SampleComponent/components/OrgChartEditor.tsx`**
   - Added context prop and data loading state management
   - Implemented async data loading with loading indicator
   - Updated diagram initialization to wait for data

4. **`SampleComponent/ControlManifest.Input.xml`**
   - Enabled WebAPI feature for CRM communication

### CRM Entity Fields Used

#### new_ocrdocument
- `new_ocrdocumentid` - Document ID
- `new_name` - Document name
- `_new_ocrheaderid_value` - OCR Header ID (for filtering)

#### new_ocrdocumentmapping  
- `new_ocrdocumentmappingid` - Mapping ID
- `new_displayfieldname` - Field display name
- `new_displayvalue` - Display value
- `new_fieldtype` - Field type
- `new_field` - Field logical name
- `new_used` - Whether field is used
- `new_confidence` - Confidence score
- `new_value` - Raw value

## Usage

### Development Mode
When running on localhost, the component automatically uses static test data:

```typescript
// Automatically detected - no configuration needed
const result = await loadData(context);
// Returns static testData and connections
```

### Production Mode
When deployed to CRM, the component fetches live data for the current OCR header:

```typescript
// Automatically uses CRM API with current OCR header ID
const result = await loadData(context, headerEntityId);
// Returns live data for all documents in the current OCR header
// Uses filter: _new_ocrheaderid_value eq {headerEntityId}
```

## Error Handling

- CRM API failures automatically fall back to static data
- Loading states are displayed to users during data fetching
- Console logging provides debugging information

## Performance Considerations

- **Header-Scoped Loading**: Only fetches documents for the current OCR header, not all documents
- Only essential fields are fetched from CRM
- Uses efficient OData filtering: `$filter=_new_ocrheaderid_value eq {headerEntityId}`
- Data is cached in component state to avoid repeated API calls
- Loading indicators provide user feedback during data fetching

## Testing

### Development Testing
1. Run `npm run start:watch`
2. Component will use static test data
3. Verify all functionality works with test data

### Production Testing
1. Deploy component to CRM environment
2. Verify component fetches live data from CRM entities
3. Test error handling by temporarily breaking CRM connection

## Future Enhancements

- Add data caching for improved performance
- Implement real-time data updates
- Add configuration options for data source selection
- Support for additional CRM entities
